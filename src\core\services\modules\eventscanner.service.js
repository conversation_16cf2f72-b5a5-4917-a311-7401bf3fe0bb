import { ApiConfig, EnvConfig } from "../../../configs/base.config";
import { httpService } from "../http.service";

function fetchData(obj, token) {
  try {
    const url = ApiConfig.App.Events.CheckQRCODE;
      console.log(url);
    return httpService._postWithToken(url, obj, token);
  } catch (e) {
    console.log(e);
  }
}

function fetchCheckinData(obj, token) {
  try {
    const url = ApiConfig.App.Events.dootlistCheckIn;
      console.log(url);
    return httpService._postWithToken(url, obj, token);
  } catch (e) {
    console.log(e);
  }
}

export default {
  fetchData,
  fetchCheckinData
};