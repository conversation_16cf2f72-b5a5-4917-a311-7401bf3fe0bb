import React, { Component, useState, useEffect } from "react";
import {
  View,
  Text,
  Image,
  Dimensions,
  RefreshControl,
  TextInput,
  SafeAreaView
} from "react-native";
import { Spinner } from "@share";
import styles from './styles';
import _ from "lodash";
import { Color, Styles, Constants } from "@common";

import AlphabetList from "react-native-flatlist-alphabet";

import TicketItem from '@commonComponent/ticket/ticketItem'
import HeadingText from "@share/Heading/HeadingText"

function DoorlistScreen(props){
  const [doorlist, setDoorlist] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filterSearch, onChangeText] = useState('');
  const item = props.eventscanner.selectedEvent;

  const getData = () => {
    let filterObj = {
      event_date_id: item.id,
    }
    props.getDoorlist(filterObj);
  }

  useEffect(() => {
    setDoorlist(props.doorlist.data)
  }, [props.doorlist.data]);

  useEffect(() => {
    if(filterSearch){
      var newDoorlist = props.doorlist.data.filter((item)=>{
        return (item.key.toLowerCase().indexOf(filterSearch.toLowerCase()) >= 0) || (item.item.cart_temp.phone.toLowerCase().indexOf(filterSearch.toLowerCase()) >= 0); 
      })
      setDoorlist(newDoorlist)
    }else{
      setDoorlist(props.doorlist.data)
    }
  }, [filterSearch]);

  useEffect(() => {
    getData()
  }, []);

  const _onRefresh = () => {
    setDoorlist([])
    getData();
  }

  const _onSelectItem = (item) => {
    console.log(item);
    props.setTicket(item);
    props.navigation.navigate("TicketDoorlistDetail");
  }

  const renderListItem = (item) => {
    return (
      <TicketItem item={item.item} onPress={() => _onSelectItem(item.item)}/>
    );
  };

  const renderSectionHeader = (section) => {
    return (
      <View style={styles.sectionHeaderContainer}>
        <Text style={styles.sectionHeaderLabel}>{section.title}</Text>
      </View>
    );
  };

  return (
    <View style={{flex: 1}}>
      <View style={{padding: Constants.paddingScreen}}>
        <HeadingText title={item.event_stage.event.name_vi}/>
        <TextInput
          editable
          onChangeText={onChangeText}
          style={styles.inputView}
        />
      </View>
      <SafeAreaView style={{flex: 1}}>
        {
          doorlist.length >= 0 ?
            <AlphabetList
              style={{flex: 1}}
              data={doorlist}
              extraData={doorlist}
              renderItem={renderListItem}
              renderSectionHeader={renderSectionHeader}
              getItemHeight={() => 40}
              sectionHeaderHeight={30}
              letterItemStyle={{ height: 14}}
              indexLetterSize={10}
              indexLetterColor={Color.primary}
              refreshControl={
                <RefreshControl
                    refreshing={loading}
                    onRefresh={_onRefresh.bind(this)}
                />
              }
            />
          :
          <View style={{flex: 1, paddingVertical: 10, alignItems: 'center'}}>
            <Spinner mode="normal" size="small" />
          </View>
        }
        
      </SafeAreaView>
    </View>
  );
}

export default DoorlistScreen;
