import React, {PureComponent} from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Images, Constants, Device, Styles, Color } from "@common";
import Entypo from 'react-native-vector-icons/Entypo';
import moment from 'moment'


const styles = StyleSheet.create({
  viewWrap: {
    marginBottom: 10,
  },
  itemWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#E4E5E5',
    borderRadius: 10
  },
  infoWrap: {
    backgroundColor: '#D9D9D9',
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 12,
    width: Constants.Window.width - (Constants.paddingScreen * 2) - 74
  },
  timeTxt: {
    fontSize: 12,
    color: '#000',
    fontFamily: Constants.fontFamily
  },
  dateTxt: {
    fontSize: 20,
    fontFamily: Constants.fontFamilyBold,
  },
  timeWrap: {
    width: 74,
  },
  titleItem: {
    fontSize: 12,
    color: '#000',
    fontFamily: Constants.fontFamilyBold,
  },
  eventStatusWrapper: {
    paddingVertical: 4,
  },
  eventStatus: {
    fontSize: 10,
    fontFamily: Constants.fontFamily,
    color: '#212121',
    paddingLeft: 4,
  },
  infoItem:{
    fontSize: 10,
    fontFamily: Constants.fontFamily,
    color: '#212121'
  }

});

const EventItem = ({item, onPress}) => {
  const show_date_month = moment(item.show_date * 1000).format('MM');
  const show_date_date = moment(item.show_date * 1000).format('D');
  const show_date_hour = moment(item.show_date * 1000).format('HH');
  const show_date_minute = moment(item.show_date * 1000).format('mm');
  return (
    <TouchableOpacity style={styles.viewWrap} onPress={() => onPress(item)}>
      <View style={[Styles.Common.RowCenterBetween, styles.itemWrap]}>
        <View style={[Styles.Common.ColumnCenter, styles.timeWrap]}>
          <Text style={styles.timeTxt}>Tháng {show_date_month}</Text>
          <Text style={styles.dateTxt}>{show_date_date}</Text>
          <Text style={styles.timeTxt}>{show_date_hour}h{show_date_minute}</Text>
        </View>
        <View style={[styles.infoWrap, Styles.RowCenterTop]}>
          <Text style={styles.titleItem}>{item.event_stage.event.name_vi}</Text>
          {
            item.allow_check_in == 1 &&
              <View style={[Styles.Common.RowCenterLeft, styles.eventStatusWrapper]}>
                <Entypo name="controller-record" size={10} color={Color.bodyColor}/>
                <Text style={styles.eventStatus}>Chưa soát vé</Text>
              </View>
          }
          {
            item.allow_check_in == 2 &&
              <View style={[Styles.Common.RowCenterLeft, styles.eventStatusWrapper]}>
                <Entypo name="controller-record" size={10} color={Color.primary}/>
                <Text style={styles.eventStatus}>Đang soát vé</Text>
              </View>
          }
          {
            item.allow_check_in == 3 &&
              <View style={[Styles.Common.RowCenterLeft, styles.eventStatusWrapper]}>
                <Entypo name="controller-record" size={10} color={Color.bodyColor}/>
                <Text style={styles.eventStatus}>Đã soát vé</Text>
              </View>
          }
          
          <Text style={styles.infoItem}>Số vé đã soát: {item.total_checked_in}/{item.total_ticket}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

export default EventItem;