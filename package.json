{"name": "RN_VES", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.23.1", "@react-native-masked-view/masked-view": "^0.2.6", "@react-navigation/bottom-tabs": "^6.0.5", "@react-navigation/drawer": "^6.1.4", "@react-navigation/native": "^6.0.2", "@react-navigation/native-stack": "^6.1.0", "@react-navigation/stack": "^6.0.7", "add": "^2.0.6", "axios": "^0.21.1", "deprecated-react-native-prop-types": "^5.0.0", "lodash": "^4.17.21", "moment": "^2.29.1", "numeral": "^2.0.6", "react": "18.2.0", "react-native": "0.73.6", "react-native-auto-height-image": "^3.2.4", "react-native-barcode-builder": "^2.0.0", "react-native-camera": "^4.2.1", "react-native-dropdownalert": "^4.3.0", "react-native-easy-toast": "^2.0.0", "react-native-flatlist-alphabet": "^1.1.2", "react-native-gesture-handler": "^2.15.0", "react-native-image-blur-loading": "^1.2.1", "react-native-keyboard-aware-scroll-view": "github:APSL/react-native-keyboard-aware-scroll-view#pull/501/head", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.0.4", "react-native-maps": "^1.10.3", "react-native-permissions": "^4.1.5", "react-native-progressive-image": "^1.0.4", "react-native-push-notification": "^8.1.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-reanimated": "^3.8.1", "react-native-render-html": "^6.0.5", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "^3.29.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-snap-carousel": "^3.9.1", "react-native-storage": "^1.0.1", "react-native-store-version": "^1.4.1", "react-native-swipe-list-view": "^3.2.9", "react-native-vector-icons": "^10.0.3", "react-native-video": "^5.1.1", "react-redux": "^7.2.4", "redux": "^4.1.1", "redux-persist": "^6.0.0", "redux-saga": "^1.1.3", "start": "^5.1.0", "validate.js": "^0.13.1", "yarn": "^1.22.21"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}