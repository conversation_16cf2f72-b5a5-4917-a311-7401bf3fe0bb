import {
    LOGIN_REQUEST,
    LOGIN_SUCCESS,
    LOGIN_FAILURE,
} from "../../actions/types.action";

import services from "../../services/modules/login.service";
import {put, call, takeLatest} from "redux-saga/effects";
import {logEventAppier} from '../../../configs/logEventAppier'

function* doLogin(obj) {
    try {
        const res = yield call(services.doLogin, obj.payload);
        console.log(res);
        if ((res.status === 200 || res.status === 201) && res.data !== null && res.data.success !== false) {
            console.log('LOGIN DETAIL');
            var customerDetail = res.data;

            yield put({
                type: LOGIN_SUCCESS,
                payload: res.data
            });
        } else {
            yield put({
                type: LOGIN_FAILURE,
                payload: res.data
            });
        }
    } catch (error) {
        yield put({
            type: LOGIN_FAILURE,
            payload: error
        });
    }
}

export function* watchLogin() {
    yield takeLatest(LOGIN_REQUEST, doLogin);
}
