import React, {Component} from "react";
import {
    ScrollView,
    Text,
    View,
    StyleSheet,
    Image,
    TouchableOpacity,
    Linking
} from "react-native";
import Feather from 'react-native-vector-icons/Feather'
import {Styles, Images, Constants, Device} from "@common";
import {connect} from "react-redux";

class CustomDrawerContent extends Component {

    constructor(props) {
        super(props);
        this.state = {
            activeMenu: "home",
            showShop: false,
            showBuyingGuide: false,
            categories: [],
            social: [],
            shop: [],
            socialUrl: "",
            showSocialNetwork: false
        };
    }

    componentDidMount() {
        let configSite = this.props.configSite && this.props.configSite;
        let menu = configSite.menu
            ? configSite.menu.filter(item => {
                return item.page_type === "category";
            })
            : [];
        if (menu.length > 0) {
            this.setState({
                categories: menu[0].childs
            });
        }
        if(configSite)
            this.setState({
                social: configSite.social,
                shop: configSite.shop
            });
    }

    render() {
        var {navigation} = this.props;
        return (
            <View style={styles.container}>
                <View style={styles.logoOuter}>
                    <View style={styles.logo}>
                        <Image
                            source={Images.mainLogo}
                            style={styles.image}
                            resizeMode="contain"
                        />
                    </View>
                </View>
                <ScrollView>
                    <View style={styles.sideMenu}>
                        <TouchableOpacity
                            activeOpacity={0.8}
                            onPress={() => {
                                this.setState({activeMenu: "home"});
                                navigation.navigate("HomeStack");
                                navigation.closeDrawer();
                            }}
                        >
                            <View
                                style={[
                                    styles.menuItem,
                                    {
                                        backgroundColor: this.state.activeMenu === "home" ? "#eee" : "#fff"
                                    }
                                ]}
                            >
                                <View style={styles.menuIcon}>
                                    <Image
                                        style={styles.icon}
                                        source={Images.drawerComponent.home}
                                    />
                                </View>
                                <Text style={styles.menuItemCaption}>Trang chủ</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}
const mapStateToProps = state => {
    return {
        configSite: state.home.configSite,
    };
};
export default connect(mapStateToProps)(CustomDrawerContent);
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#fff"
    },
    logoOuter: {
        padding: 30,
        borderBottomWidth: 1,
        borderColor: "#eee",
        alignItems: "center",
        paddingTop: 45
    },
    image: {
        width: 200,
        height: 70,
        resizeMode: "contain"
    },
    sideMenu: {flex: 1},
    menuItem: {
        height: 50,
        alignItems: "center",
        paddingLeft: 20,
        flexDirection: "row",
        borderBottomWidth: 1,
        borderBottomColor: "#eee"
    },
    menuItemCaption: {
        color: "#222",
        marginLeft: 20,
        fontFamily: Constants.fontFamily
    },
    icon: {width: 18, height: 18, resizeMode: "contain"},
    socialImageOuter: {
        width: 32,
        height: 32,
        justifyContent: "center",
        alignItems: "center",
        marginRight: 20
    },
    socialImage: {
        width: 16,
        height: 16
    },
    iconArrow: {color: "#999"},
    footer: {
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f1f1f1",
        padding: 10,
        paddingBottom: Device.isIphoneX ? 30 : 10
    },
    footerCaption: {fontFamily: Constants.fontFamily, color: "#999"}
});
