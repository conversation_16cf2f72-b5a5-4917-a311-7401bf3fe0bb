import React from "react";
import { connect } from "react-redux";

// import { createAppContainer, createSwitchNavigator } from "react-navigation";
import { createSwitchNavigator } from '@react-navigation/compat';

import { Icons, Styles, Images, Color } from "@common";
// import { createStackNavigator } from "react-navigation-stack";
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createStackNavigator, TransitionPresets } from '@react-navigation/stack';

// import { createBottomTabNavigator } from "react-navigation-tabs";
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

// import { createDrawerNavigator } from "react-navigation-drawer";
import { createDrawerNavigator } from '@react-navigation/drawer';

import LoginContainer from "@containers/login";
import HomeContainer from "@containers/home";
import EventListContainer from "@containers/eventlist"
import EventScanner from "@containers/eventscanner"
import TicketDetail from "@containers/ticketdetail";
import TicketDoorlistDetailContainer from '@containers/ticketdoorlistdetail'
import DoorListContainer from "@containers/doolist";

import DrawerContent from "@share/DrawerContent/index";
import TabBarIcon from "@share/TabbarIcon";
import SplashScreen from "@screens/splash";

import HeaderNavWithLogo from "@commonComponent/headerNavWithLogo";
import { NavigationContainer, useNavigationContainerRef } from '@react-navigation/native';


const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();
const Drawer = createDrawerNavigator();

// V5
function DrawerStack(){
    return(
        <Stack.Navigator headerMode="screen" initialRouteName="EventListScreen" >
            <Stack.Screen name="EventListScreen" component={EventListContainer} options={{
                header: ({ scene, previous, navigation }) => {
                    return (
                        <HeaderNavWithLogo navigation={navigation} />
                    );
                }
            }}/>
            <Stack.Screen name="AppDrawer" component={DashboardTabNavigator} options={{headerShown: false}} />
            <Stack.Screen name="AppDrawerOrg" component={DashboardTabOrgNavigator} options={{headerShown: false}} />
        </Stack.Navigator>
    );
};

// V5
function DashboardTabOrgNavigator(){
    return(
        <Tab.Navigator initialRouteName="Scanner" screenOptions={{
            tabBarShowLabel: false,
            tabBarStyle: {
                backgroundColor: "#fff"
            },
        }}>
            <Tab.Screen name="Dashboard" component={HomeContainer} options={{
                tabBarLabel: '',
                tabBarIcon: ({ focused }) => <TabBarIcon image="home" title="Dashboard" focused={focused} />,
                tabBarActiveBackgroundColor: Color.primary,
                header: ({ scene, previous, navigation }) => {
                    return (
                        <HeaderNavWithLogo navigation={navigation} />
                    );
                }
            }} />
            <Tab.Screen name="Scanner" component={EventScanner} options={{
                tabBarLabel: '',
                tabBarIcon: ({ focused }) => <TabBarIcon image="scanner" title="Scanner" focused={focused} />,
                tabBarActiveBackgroundColor: Color.primary,
                header: ({ scene, previous, navigation }) => {
                    return (
                        <HeaderNavWithLogo navigation={navigation} />
                    );
                }
            }} />
            <Tab.Screen name="Doorlist" component={DoorListContainer} options={{
                tabBarLabel: '',
                tabBarIcon: ({ focused }) => <TabBarIcon image="bell" title="Doorlist" focused={focused} counter={4} />,
                tabBarActiveBackgroundColor: Color.primary,
                tabBarHideOnKeyboard: true,
                header: ({ scene, previous, navigation }) => {
                    return (
                        <HeaderNavWithLogo navigation={navigation} />
                    );
                }
            }} />
        </Tab.Navigator>
    );
};

function DashboardTabNavigator(){
    return(
        <Tab.Navigator initialRouteName="Scanner" screenOptions={{
            tabBarShowLabel: false,
            tabBarStyle: {
                backgroundColor: "#fff"
            },
        }}>
            <Tab.Screen name="Scanner" component={EventScanner} options={{
                tabBarLabel: '',
                tabBarIcon: ({ focused }) => <TabBarIcon image="scanner" title="Scanner" focused={focused} />,
                tabBarActiveBackgroundColor: Color.primary,
                header: ({ scene, previous, navigation }) => {
                    return (
                        <HeaderNavWithLogo navigation={navigation} />
                    );
                }
            }} />
            <Tab.Screen name="Doorlist" component={DoorListContainer} options={{
                tabBarLabel: '',
                tabBarIcon: ({ focused }) => <TabBarIcon image="bell" title="Doorlist" focused={focused} counter={4} />,
                tabBarActiveBackgroundColor: Color.primary,
                tabBarHideOnKeyboard: true,
                header: ({ scene, previous, navigation }) => {
                    return (
                        <HeaderNavWithLogo navigation={navigation} />
                    );
                }
            }} />
        </Tab.Navigator>
    );
};

// V5 co DrawerOptions sẵn rồi
function AppDrawerNavigator(){
    return (
        <Drawer.Navigator initialRouteName="EventListScreen" drawerContent={(props) => <DrawerContent {...props} />}>
            <Drawer.Screen name="DrawerStack" component={DrawerStack} options={{headerShown: false}} />
        </Drawer.Navigator>
    )
};

const RootNavigator = () => {

    const navigationRef = useNavigationContainerRef();
    return (
        <NavigationContainer ref={navigationRef}>
            <Stack.Navigator initialRouteName="SplashScreen">
                <Stack.Screen name="AppDrawerNavigator" options={{headerShown: false}} component={AppDrawerNavigator}/>
                <Stack.Screen name="SplashScreen" options={{headerShown: false}} component={SplashScreen}/>
                <Stack.Screen name="Login" options={{headerShown: false}} component={LoginContainer}/>
                <Stack.Group name="ModalScreen" screenOptions={{ presentation: 'fullScreenModal' }}>
                    <Stack.Screen name="TicketDetail" options={{headerShown: false}} component={TicketDetail} />
                    <Stack.Screen name="TicketDoorlistDetail" options={{headerShown: false}} component={TicketDoorlistDetailContainer} />
                </Stack.Group>
            </Stack.Navigator>
        </NavigationContainer>
    );
}

export default Navigator = RootNavigator;
