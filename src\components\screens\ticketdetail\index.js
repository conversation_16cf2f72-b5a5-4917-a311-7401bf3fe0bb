import React, { useCallback, useState, useRef } from "react";
import {
  Text,
  Dimensions,
  TouchableOpacity,
  View,
  SafeAreaView,
  ScrollView,
  BackHandler
} from "react-native";
import moment from 'moment'
import {Styles, Constants, Color} from "@common";
import styles from './styles';
import HeadingText from "@share/Heading/HeadingText"
import Ionicons from 'react-native-vector-icons/Ionicons'
import HeadingTwo from '@share/Heading/HeadingTwo'
import RowInfo from '@share/RowInfo/RowInfo'
import {Spinner, ButtonIndex, Button} from "@share";

const windowW = Dimensions.get("window").width;

function TicketDetail(props) {
    const { data } = props.route.params.item;
    console.log(data)

    const onCloseModal = () => {
        props.emptyTicket()
        if(props.navigation.canGoBack()){
            props.navigation.goBack()
        }else{
            props.navigation.navigate('AppDrawerNavigator', {
                screen: 'EventListScreen'
            })
        }
    }
    const show_round = (data && data.event_date) ? moment(data.event_date.show_date * 1000).format('ddd D MMM YYYY HH:mm') : '';

    return (
        <SafeAreaView
            style={Styles.containerView}
            >
            <ScrollView style={Styles.scrollViewWrapper}>
                <HeadingText title={data.event.meta_title_vi}/>
                <View style={[styles.validWrap, Styles.Common.ColumnCenter]}>
                    <Text style={styles.txtValid}>Valid Ticket</Text>
                    <Ionicons name="checkmark-circle" size={80} color={Color.success} /> 
                </View>
                <HeadingTwo title="BUYER INFOMATION" />
                <RowInfo label="Name:" value={data.name}/>
                <RowInfo label="NFT Code:" value={data.mint_address}/>
                <RowInfo label="Phone Number:" value={data.cart_temp.phone}/>
                <RowInfo label="Email:" value={data.cart_temp.email}/>
                <HeadingTwo title="TICKET DETAILS" />
                <RowInfo label="Order Number:" value={data.cart_temp.timer_ticket}/>
                <RowInfo label="Ticket Type:" value=""/>
                <RowInfo label="Show Round:" value={show_round}/>
                <RowInfo label="Zone:" value={data.zone_name}/>
                <RowInfo label="Seat Number:" value={data.seat_name}/>
            </ScrollView>
            <View style={[{padding: Constants.paddingScreen}, Styles.Common.ColumnCenter]}>
                <ButtonIndex
                    text="Close"
                    containerStyle={{width: 200}}
                    onPress={() => onCloseModal()}
                />
            </View>
        </SafeAreaView>
    );
}

export default TicketDetail;