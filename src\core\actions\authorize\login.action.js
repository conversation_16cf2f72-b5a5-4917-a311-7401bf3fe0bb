import {
  LOGIN_REQUEST,
  EMPTY_LOGIN,
  UPDATE_USER_INFO
} from "../types.action";

export const doLogin = (obj, loginFrom) => ({
  type: LOGIN_REQUEST,
  payload: obj
});

export const emptyLogin = () => ({
  type: EMPTY_LOGIN
});

export const updateLoginReponse = obj => ({
  type: UPDATE_USER_INFO,
  payload: {
    access_token: obj.access_token,
    success: obj.success,
    customer: {
      id: obj.customer.id,
      firstname: obj.customer.firstname,
      lastname: obj.customer.lastname,
      email: obj.customer.email,
      birthday: obj.customer.birthday,
      phone: obj.customer.phone
    }
  }
});
