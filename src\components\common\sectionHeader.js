import React, { Component } from 'react';
import { View, Text, StyleSheet} from 'react-native';
import {Constants} from '@common';

export default class SectionHeader extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  render() {
      var {title}= this.props;
    return (
       <View style={styles.pageTitle}>
            <Text style={styles.pageTitleCaption}>{title}</Text>
            <View style={styles.titleDivider}></View>
       </View>
    );
  }
}
const styles = StyleSheet.create({
    pageTitle:{
         alignItems: 'center',
         flexDirection: 'column',
         marginVertical: 30,
     },
     pageTitleCaption:{
         color:'#222',
         fontSize:22,
         fontFamily: Constants.fontHeader,
     },
     titleDivider:{
         width:80,
         height:1,
         backgroundColor:'#ddd',
         marginVertical: 15,
     },
})