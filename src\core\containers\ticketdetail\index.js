import { connect } from "react-redux";
import TicketDetail from "../../../components/screens/ticketdetail";
import {
    emptyTicket
} from "../../actions/ticketdetail/ticketdetail.actions";

const mapStateToProps = state => {
  return {
    
  };
};

const mapDispatchToProps = dispatch => {
  return {
    emptyTicket: obj => dispatch(emptyTicket(obj)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(TicketDetail);
