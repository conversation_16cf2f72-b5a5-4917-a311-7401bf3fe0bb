import React from "react";
import { View, StyleSheet, Image, Animated } from "react-native";
// import AutoHeightImage from "react-native-auto-height-image";
import { WaitingLoader } from "@common";
import { TouchableOpacity } from "react-native-gesture-handler";

const styles = StyleSheet.create({
    container: {
        marginBottom: 5,
        backgroundColor: "#f1f1f1"
    },
    imageOverlay: {
        position: "absolute",
        left: 0,
        right: 0,
        bottom: 0,
        top: 0
    }
});

class ProgressiveImage extends React.Component {
    imageAnimated = new Animated.Value(0);
    _onImageLoad = () => {
        Animated.timing(this.imageAnimated, {
            toValue: 1,
            duration: 500
        }).start();
    };
    render() {
        const { source, style, width, height, onPress, ...props } = this.props;
        return (
            <View style={[styles.container, { width: width, height: height }]}>
                <TouchableOpacity
                    activeOpacity={1}
                    onPress={() => (onPress ? onPress() : null)}
                    style={{ width: width, height: height }}
                >
                    <WaitingLoader />
                    <Animated.Image
                        {...props}
                        source={source}
                        style={[
                            styles.imageOverlay,
                            { width: width, height: height, resizeMode: "cover" },
                            { opacity: this.imageAnimated }
                        ]}
                        onLoad={this._onImageLoad}
                    />
                </TouchableOpacity>
            </View>
        );
    }
}
export default ProgressiveImage;
