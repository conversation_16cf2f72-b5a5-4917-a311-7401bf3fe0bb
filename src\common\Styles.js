/**
 * Created by InspireUI on 20/12/2016.
 *
 * @format
 */

import { Dimensions, Platform, StatusBar } from "react-native";

import Constants from "./Constants";
import Device from "./Device";
import Color from "./Color";
import Config from "./Config";

const { height, width } = Dimensions.get("window");

const Styles = {
  width: Dimensions.get("window").width,
  height: Platform.OS !== "ios" ? height : height - 20,
  navBarHeight: Platform !== "ios" ? height : 0,
  headerHeight: Platform.OS === "ios" ? 40 : 56,

  thumbnailRatio: 1.2, // Thumbnail ratio, the product display height = width * thumbnail ratio
  containerView: {
    flex: 1,
    backgroundColor: 'white'
  },
  scrollViewWrapper: {
    backgroundColor: 'white',
    paddingTop: 10,
    flex: 1,
  },
  app: {
    flexGrow: 1,
    backgroundColor: Device.isIphoneX ? "#FFF" : "#000",
    paddingTop: Device.ToolbarHeight
  },
  FontSize: {
    tiny: 12,
    small: 14,
    medium: 16,
    big: 18,
    large: 20
  },
  IconSize: {
    TextInput: 25,
    ToolBar: 18,
    Inline: 20,
    SmallRating: 14
  },
  headingView: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 5,
    paddingHorizontal: Constants.paddingScreen
  },
  headingText: {
    fontSize: 20,
    fontFamily: Constants.fontFamilyBold,
    color: 'black',
    textAlign: 'center'
  },
  headingTwoWrap: {
    paddingVertical: 5,
    paddingHorizontal: Constants.paddingScreen,
    backgroundColor: Color.primary,
  },
  txtHeadingTwo: {
    fontSize: 15,
    color: 'white',
    fontFamily: Constants.fontFamilyBold
  },
  rowWrapper: {
    paddingVertical: 8,
    paddingHorizontal: Constants.paddingScreen,
    borderBottomWidth: 1,
    borderColor: Color.borderColor
  },
  txtLabel:{
    fontSize: Platform.OS == 'ios' ? 14 : 12,
    color: 'black',
    fontFamily: Constants.fontFamilyBold,
  },
  txtValue: {
    fontSize: Platform.OS == 'ios' ? 14 : 12,
    color: 'black',
    maxWidth: width/2,

    fontFamily: Constants.fontFamily
  },
  sectionWrapper: {
    padding: Constants.paddingScreen
  },
  sectionWrapperPTop: {
    paddingTop: Constants.paddingScreen
  }
};

Styles.Common = {
  screenHeight: {
    height: height
  },
  Column: {},
  ColumnCenter: {
    justifyContent: "center",
    alignItems: "center"
  },
  ColumnCenterTop: {
    alignItems: "center"
  },
  ColumnCenterBottom: {
    justifyContent: "flex-end",
    alignItems: "center"
  },
  ColumnCenterLeft: {
    justifyContent: "center",
    alignItems: "flex-start"
  },
  ColumnCenterRight: {
    justifyContent: "center",
    alignItems: "flex-end"
  },
  ColumnCenterBetween: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "space-between"
  },
  RowCenter: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center"
  },
  RowCenterTop: {
    flexDirection: "row",
    justifyContent: "center"
  },
  RowCenterBottom: {
    flexDirection: "row",
    alignItems: "flex-end",
    justifyContent: "center"
  },
  RowCenterLeft: {
    flexDirection: "row",
    alignItems: "center"
  },
  RowCenterRight: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end"
  },
  RowCenterLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  RowCenterBetween: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  // More traits

  tabbarIcon: {
    width: 22,
    height: 22,
    resizeMode: "contain"
  },
  tabbarIconSize: 22,
  headerLogo: {
    height: 40,
    width: 40,
  },
  inputWrap: {
    flexDirection: "row",
    alignItems: "center",
    borderColor: "#eee",
    borderWidth: 1,
    marginTop: 10,
    borderRadius: 4
  },
  input: {
    color: "#222",
    height: 40,
    paddingVertical: 8,
    paddingHorizontal: 15,
    flex: 1,
    fontFamily: Constants.fontFamily
  },
  inputError: { borderColor: Color.error, backgroundColor: "#ffebee" },
  inputWrapIconRight: {
    position: "absolute",
    right: 10,
    color: "#999",
    fontSize: 16
  },
  headerSection: {
    color: "#222",
    fontSize: 22,
    fontFamily: Constants.fontHeader,
    paddingVertical: 15
  },
  carouselPaging: {
    padding: 10,
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 15,
    flexDirection: "row",
    justifyContent: "center"
  },
  carouselPagingItem: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#ddd",
    marginHorizontal: 3
  },
  carouselPagingItemActive: {
    backgroundColor: Color.primary
  },
  container: {
    width: '100%',
  },
  containerErrorMessage: {
    backgroundColor: "#ffebee",
    borderColor: "#ffcdd2",
    borderWidth: 1,
    padding: 5,
    borderRadius: 5,
  },
   oldPrice: {
    textDecorationLine: "line-through",
    color: "#999",
    marginLeft: 10,
    fontSize:11
  },
  discountLabel: {
    color: "#fff",
    backgroundColor: "#d32f2f",
    marginLeft: 10,
    paddingVertical: 2,
    paddingHorizontal:5,
    textAlign: "center",
    borderRadius:4,
    overflow:'hidden',
    fontSize:11
  }
};

export default Styles;
