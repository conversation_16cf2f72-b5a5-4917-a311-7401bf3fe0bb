class RequestService {
    constructor() {
        this.getAuthHeaders();
    }

    async getAuthHeaders(contentType, token_key) {
        let _headers = this.getHeaders(contentType ? contentType : '');
        _headers['Authorization'] = token_key
        return {headers: _headers};
    }

    getHeaders(contentType) {
        let headers = {};
        let _ctype = '';
        switch (contentType) {
        case 'json':
            _ctype = 'application/json';
            break;
        case 'form-data':
            _ctype = '';
            break;
        case 'multipart':
            _ctype = 'multipart/form-data';
            break;
        case 'urlencoded':
            _ctype = 'application/x-www-form-urlencoded';
            break;
        default:
            _ctype = 'application/json';
            break;
        }
        if (_ctype) {
            headers = Object.assign({
                'Content-Type': _ctype,
                'Access-Control-Allow-Origin':  "*",
                'Access-Control-Allow-Methods':  "GET, POST, OPTIONS, PUT, PATCH, DELETE",
                'Access-Control-Allow-Headers':  "x-access-token, Origin, X-Requested-With, Content-Type, Accept"
            });

        }

        return headers;
    }
}

export const requestService = new RequestService();
