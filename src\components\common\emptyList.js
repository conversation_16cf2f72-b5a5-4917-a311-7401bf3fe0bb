import React, {PureComponent} from 'react';
import { View, Image, StyleSheet, Text } from "react-native";
import { Images, Constants, Device, Styles } from "@common";
import Color from '../../common/Color';


const styles = StyleSheet.create({
  viewWrap: {
    padding: 20,
  },
  imageView: {
    width: 100,
    height: 64,
    marginBottom: 15,
  },
  txtView: {
    fontSize: 12,
    fontFamily: Constants.fontFamily,
    color: Color.bodyColor
  }

});

function EmptyList(){
  return (
    <View style={[styles.viewWrap, Styles.Common.ColumnCenter]}>
        <Image source={Images.noData} style={styles.imageView}/>
        <Text style={styles.txtView}><PERSON>h<PERSON>ng có dữ liệu</Text>
    </View>
  );
}

export default EmptyList;