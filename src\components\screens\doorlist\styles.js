/** @format */

import { Dimensions, StyleSheet, I18nManager, Platform } from "react-native";
import { Color, Styles, Constants } from "@common";

const { width, height } = Dimensions.get("window");

export default StyleSheet.create({
  inputView: {
    borderRadius: 30,
    height: 38,
    borderColor: Color.subColor,
    borderWidth: 1,
    marginTop: Constants.paddingScreen,
    paddingHorizontal: Constants.paddingScreen
  },

  sectionHeaderContainer: {
    height: 30,
    backgroundColor: Color.primary,
    justifyContent: 'center',
    paddingHorizontal: 15,
  },

  sectionHeaderLabel: {
    color: '#fff',
  },
});
