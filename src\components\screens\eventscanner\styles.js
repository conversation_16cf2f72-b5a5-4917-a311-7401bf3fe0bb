import { Dimensions, StyleSheet, I18nManager, Platform } from "react-native";
import { Color, Styles, Constants } from "@common";

const { width, height } = Dimensions.get("window");

export default StyleSheet.create({
    subWrap: {
        padding: 20,
        paddingBottom: 30
    },
    txtSub: {
        fontSize: 15,
        color: Color.secondary,
        fontFamily: Constants.fontFamilyBold,
        textAlign: 'center'
    },
    camWrap: {
        height: width - 80,
        width: width
    },
    camStyle: {
        height: width - 80,
    },  
    camView: {
        width: width,
        height: width - 80,
    },  
    viewFlashWrap: {
        padding: 20,
    },
    txtFlash: {
        fontSize: 12,
        fontFamily: Constants.fontFamilyBold,
        color: Color.secondary,
    }
});
