/** @format */

import React from "react";
import PropTypes from "prop-types";
import {
    StyleSheet,
    TouchableOpacity,
    View,
    Image,
    Text,
    ActivityIndicator,
    I18nManager,
} from "react-native";
import { Color, Images, Constants } from "@common";

const Button = (props) => {
    if (props.type === "border") {
        return <BorderButton {...props} />;
    } else if (props.type === "image") {
        return <ImageButton {...props} />;
    } else if (props.type === "text") {
        return <TextButton {...props} />;
    } else if (props.type === "tab") {
        return <TabButton {...props} />;
    }else if (props.type === "menu") {
        return <DrawerButton {...props} />;
    }else if (props.type === "group") {
        return <GroupButton {...props} />;
    }
    return <StandardButton {...props} />;
};

Button.propTypes = {
    type: PropTypes.string,
};

const TextButton = (props) => (
    <TouchableOpacity
        disabled={props.disabled || props.isLoading}
        onPress={() => props.onPress()}
        style={[
            styles.button,
            props.style,
            props.inactive && { backgroundColor: "#C6D8E4" },
        ]}
        activeOpacity={0.9}
        underlayColor="#ccc">
        <View style={styles.buttonView}>
        {props.icon && (
            <Image
            source={props.icon}
            defaultSource={props.defaultSource}
            style={[
                styles.imageIcon,
                { tintColor: props.color },
                I18nManager.isRTL && {
                transform: [{ rotate: "180deg" }],
                },
            ]}
            />
        )}
        <Text {...props} style={[styles.text, props.textStyle]}>
            {props.text}
        </Text>
        {props.isLoading && (
            <ActivityIndicator style={styles.loading} color="#FFF" />
        )}
        </View>
    </TouchableOpacity>
);

const BorderButton = (props) => (
    <TouchableOpacity
        disabled={props.disabled || props.isLoading}
        onPress={() => props.onPress()}
        style={[
            styles.button,
            props.style,
            props.inactive && { backgroundColor: "#C6D8E4" },
        ]}
        activeOpacity={0.9}
        underlayColor="#ccc">
        <View style={styles.buttonView}>
        {props.icon && (
            <Image
            source={props.icon}
            defaultSource={props.defaultSource}
            style={[
                styles.imageIcon,
                { tintColor: props.color },
                I18nManager.isRTL && {
                transform: [{ rotate: "180deg" }],
                },
            ]}
            />
        )}
        <Text {...props} style={[styles.text, props.textStyle]}>
            {props.text}
        </Text>
        {props.isLoading && (
            <ActivityIndicator style={styles.loading} color="#FFF" />
        )}
        </View>
    </TouchableOpacity>
);

const StandardButton = (props) => (
    <TouchableOpacity
        disabled={props.disabled || props.isLoading}
        onPress={() => props.onPress()}
        style={[
            styles.button,
            props.style,
            props.inactive && { backgroundColor: "#C6D8E4" },
        ]}
        activeOpacity={0.9}
        underlayColor="#ccc">

        <View style={[styles.buttonView, props.buttonView]}>
        {props.icon && (
            <Image
            source={props.icon}
            defaultSource={props.defaultSource}
            style={[
                styles.imageIcon,
                { tintColor: props.color },
                I18nManager.isRTL && {
                transform: [{ rotate: "180deg" }],
                },
            ]}
            />
        )}
        <Text {...props} style={[styles.text, props.textStyle]}>
            {props.text}
        </Text>
        {props.isLoading && (
            <ActivityIndicator style={styles.loading} color="#FFF" />
        )}
        </View>
    </TouchableOpacity>
);

const ImageButton = (props) => (
    <TouchableOpacity
        disabled={props.disabled}
        onPress={() => props.onPress()}
        activeOpacity={0.8}
        underlayColor="#eeeeee"
        style={props.buttonStyle}>
        <Image
        {...props}
        defaultSource={props.defaultSource}
        style={[
            props.imageStyle,
            props.isAddWishList && { tintColor: Color.heartActiveWishList },
            props.isAddToCart && { tintColor: Color.product.TabActive },
        ]}
        resizeMode="contain"
        />
    </TouchableOpacity>
);

const GroupButton = (props) => (
    <View style={styles.wrapgroup}>
        <TouchableOpacity
            disabled={props.activeBtn == 1}
            onPress={() => props.onToggleGroup()}
            style={[styles.groupButtonStyle, (props.activeBtn == 1 && styles.activeGroup)]}>
                <Text style={[styles.txtGroup, (props.activeBtn == 1 && styles.txtGroupActive)]}>{props.leftTxt}</Text>
        </TouchableOpacity>
        <TouchableOpacity
            disabled={props.activeBtn == 2}
            onPress={() => props.onToggleGroup()}
            style={[styles.groupButtonStyle, (props.activeBtn == 2 && styles.activeGroup)]}>
                <Text style={[styles.txtGroup, (props.activeBtn == 2 && styles.txtGroupActive)]}>{props.rightTxt}</Text>
        </TouchableOpacity>
    </View>
);

const DrawerButton = (props) => (
    <TouchableOpacity 
        onPress={() => props.onPress()} 
        style={props.style}
        >
        <Image source={require('../../../assets/images/icons/menu.png')} style={{width:20, height:20, resizeMode:'contain'}}/>
    </TouchableOpacity>
)

const styles = StyleSheet.create({
    button: {
        backgroundColor: Color.primary,
        justifyContent: "center",
        alignItems: "center",
    },
    buttonView: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
    },
    imageIcon: {
        resizeMode: "contain",
        width: 20,
        marginRight: 8,
    },
    text: {
        color: "white",
        fontSize: 17,
        marginTop: 3,
    },
    borderButton: {
        height: 25,
        justifyContent: "center",
        alignItems: "center",
        borderWidth: 1,
        borderRadius: 5,
        borderColor: "white",
    },
    tabButton: {
        height: 50,
        justifyContent: "center",
    },
    tabButtonText: {
        marginLeft: 10,
        marginRight: 10,
        textAlign: "center",
        fontSize: 12,
    },
    loading: {
        marginLeft: 5,
    },
    wrapgroup: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderColor: '#d9d9d9',
        borderRadius: 20,
        borderWidth: 1,
        width: 170
    },
    groupButtonStyle: {
        flex: 1,
        borderRadius: 20,
        backgroundColor: 'white',
        height: 42,
        opacity: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    activeGroup: {
        backgroundColor: '#FF3857'
    },
    txtGroup: {
        color: 'white',
        fontSize: 15,
        fontFamily: Constants.fontFamilyBold,
    },
    txtGroup: {
        color: '#d9d9d9',
        fontSize: 15,
        fontFamily: Constants.fontFamilyBold,
    },
    txtGroupActive: {
        color: 'white',
    }
});

export default Button;
