import { ApiConfig, EnvConfig } from "../../../configs/base.config";
import { httpService } from "../http.service";

function fetchDataDoorlist(obj, token) {
  console.log('SERVICES');
  try {
    const url = ApiConfig.App.Doorlist.GetDoorlist.replace(
      "{{event_date_id}}",
      obj.event_date_id
    );
    return httpService._getWithToken(url, token);
  } catch (e) {
    console.log(e);
  }
}

export default {
  fetchDataDoorlist
};