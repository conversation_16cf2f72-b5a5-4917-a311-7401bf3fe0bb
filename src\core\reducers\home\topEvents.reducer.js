import * as Types from '../../actions/types.action';

const initialState = {
    data: [],
    isFetching: false,
    isError: false,
};

const topEventsReducer = (state = initialState, action) => {
    switch (action.type) {
        case Types.EVENTS: {
            return {
                ...state,
                isFetching: true,
                isError: false,
            };
        }
         case Types.EVENTS_SUCCESS: {
            return {
                ...state,
                isFetching: false,
                isError: false,
                data: action.payload
            };
        }
        default: {
            return state;
        }
    }
};

export default topEventsReducer;