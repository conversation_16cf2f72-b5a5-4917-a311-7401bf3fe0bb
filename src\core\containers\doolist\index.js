import { connect } from "react-redux";
import DoorlistScreen from "@screens/doorlist";
import {
  fetchDoorlist
} from "../../actions/doorlist/doorlist.actions";
import {
  emptyTicket,
  setTicket
} from "../../actions/ticketdetail/ticketdetail.actions";

const mapStateToProps = state => {
  return {
    eventscanner: state.eventscanner,
    doorlist: state.doorlist,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    emptyTicket: () => dispatch(emptyTicket()),
    setTicket: (item) => dispatch(setTicket(item)),
    getDoorlist: obj => dispatch(fetchDoorlist(obj)),
    // selectEventDetail: item => dispatch(selectEventDetail(item))
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(DoorlistScreen);
