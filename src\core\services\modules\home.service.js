import { ApiConfig } from "../../../configs/base.config";
import { httpService } from "../http.service";
import axios from "axios";

function fetchDashboard(obj, token) {
  try {
    const url = ApiConfig.App.Events.Dashboard.replace(
      "{{event_date_id}}",
      obj.event_date_id
    );
      console.log(url);
    return httpService._getWithToken(url, token);
  } catch (e) {
  }
}

export const fetchConfigSite = () => {
  // try { 
  //   return httpService._get(ApiConfig.App.AllConfigSite.ConfigSite);
  // } catch {
  //   return null;
  // }
};


export default {
  fetchDashboard
};