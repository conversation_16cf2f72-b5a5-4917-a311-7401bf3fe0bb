import React, { Component, useState, useEffect } from "react";
import {
  View,
  Text,
  Image,
  Dimensions,
  ScrollView,
  RefreshControl
} from "react-native";
import _ from "lodash";

import {Styles, Constants} from "@common";
import HeadingText from "@share/Heading/HeadingText"
import TotalCheckin from "@commonComponent/event/totalCheckin";
import TotalCheckinByType from '@commonComponent/event/totalCheckinByType'
import TotalCheckinByAccount from '@commonComponent/event/totalCheckinByAccount'
import TicketSales from '@commonComponent/event/ticketSales'
import TicketDetail from '@commonComponent/event/ticketDetail'
import ServiceFee from '@commonComponent/event/serviceFee'

function HomeScreen(props){
  const [dashboard, setDashboard] = useState(props.dashboard);
  const [loadMore, setLoadMore] = useState(false);
  const item = props.eventscanner.selectedEvent;
  
  const getData = () => {
    let filterObj = {
      event_date_id: item.id,
    }
    props.fetchDashboard(filterObj);
  }

  useEffect(() => {
    getData()
  }, []);

  useEffect(() => {
    console.log(props.dashboard)
    setDashboard(props.dashboard)
  }, [props.dashboard]);

  const _onRefresh = () => {
    getData();
  }

  return (
    <ScrollView enableOnAndroid style={{ backgroundColor: "#fff" }}
      contentContainerStyle={{paddingVertical: Constants.paddingScreen}}
      refreshControl={
        <RefreshControl
            refreshing={loadMore}
            onRefresh={_onRefresh.bind(this)}
        />
      }
    >
      <HeadingText title={item.event_stage.event.name_vi}/>
      {/* TOTAL CHECK IN - STATELESS */}
      <TotalCheckin current={dashboard.data.totalCheckedin ? dashboard.data.totalCheckedin : 0} total={dashboard.data.totalTicketSold ? dashboard.data.totalTicketSold : 0}/>
      {/* TOTAL CHEKED IN BY TICKET TYPE - STATELESS */}
      <TotalCheckinByType data={dashboard.data.zone_detail}/>
      {/* TOTAL CHECKED IN BY ACCOUNT - STATELESS */}
      {/* <TotalCheckinByAccount /> */}
      {/* TICKET SALES - STATELESS */}
      <TicketSales total={dashboard.data.totalTicketIssued} sold={dashboard.data.totalTicketSold} revenue={dashboard.data.totalRevenue}/>
      {/* DETAILS - STATELESS */}
      <TicketDetail data={dashboard.data.zone_detail}/>
      {/* SERVICES FEE - STATELESS */}
      <ServiceFee service_fee={dashboard.data.service_fee ? dashboard.data.service_fee : '7% + 10.000đ/vé'}/>
      {/* REVENUE BY DAY - STATELESS - CHUA LAM*/}
    </ScrollView>
  );
}

export default HomeScreen;
