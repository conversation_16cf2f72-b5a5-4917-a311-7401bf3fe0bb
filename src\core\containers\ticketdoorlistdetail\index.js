import { connect } from "react-redux";
import TicketDoorlistDetail from "@screens/ticketdoorlistdetail";
import {
    emptyTicket
} from "../../actions/ticketdetail/ticketdetail.actions";
import {
    checkinTicket
} from "../../actions/eventscanner/eventscanner.actions";

const mapStateToProps = state => {
  return {
    eventscanner: state.eventscanner
  };
};

const mapDispatchToProps = dispatch => {
  return {
    emptyTicket: obj => dispatch(emptyTicket(obj)),
    checkinTicket: obj => dispatch(checkinTicket(obj))
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(TicketDoorlistDetail);
