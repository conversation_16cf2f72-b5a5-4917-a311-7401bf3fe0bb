import {Platform} from 'react-native';
/**
 * example of usage:
  
  const originalHtml = "<div/>"
  const css = generateAssetsFontCss("Roboto-Dark", "ttf")
 
  const html = addCssToHtml(originalHtml, css)
 * 
 * @param fontFileName - font name in resources
 * @param fileFormat - ttf or otf
 * @returns {string} - css for webview
 */
export const generateAssetsFontCss = (
	fontFileName,
	fileFormat = "ttf"
) => {
	const fileUri = Platform.select({
		ios: `${fontFileName}.${fileFormat}`,
		android: `file:///assets/fonts/${fontFileName}.${fileFormat}`
	});

	return `
	@font-face {
        font-family: '${fontFileName}';
        src: local('${fontFileName}'), url('${fileUri}') format('${
		fileFormat === "ttf" ? "truetype" : "opentype"
	}');
	}
	`;
};