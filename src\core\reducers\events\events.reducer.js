import {EVENT_LIST_REQUEST, EVENT_LIST_SUCCESS, EVENT_LIST_FAILURE} from '../../actions/types.action';

const initialState = {
    data: {},
    isFetching: true,
    isError: false,
};
const eventsReducer = (state = initialState, action) => {
    switch (action.type) {
        case EVENT_LIST_REQUEST: {
            return {
                ...state,
                isFetching: true,
                isError: false
            };
        }
        case EVENT_LIST_SUCCESS: {
            return {
                ...state,
                isFetching: false,
                isError: false,
                data: action.payload
            };
        }
        case EVENT_LIST_FAILURE: {
            return {
                ...state,
                isFetching: false,
                isError: false
            };
        }
        default: {
            return state;
        }
    }
};

export default eventsReducer;