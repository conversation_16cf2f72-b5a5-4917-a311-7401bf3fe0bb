import React, { PureComponent } from 'react';
import {ActivityIndicator,View} from 'react-native';

export default class WaitingLoader extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  render() {
    var {style, size} = this.props;
    return (
      <View style={[{flex:1, alignItems:'center', justifyContent:'center'},{style}]}>
            <ActivityIndicator size={size}/>
      </View>
    );
  }
}
