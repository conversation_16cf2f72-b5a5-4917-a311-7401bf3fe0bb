import React, { PureComponent } from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { Images, Constants, Device } from "@common";

export default class DialogHeader extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    var { title, onClose, style } = this.props;
    return (
      <View
        style={[
          {
            paddingVertical: 30,
            paddingHorizontal: 15,
            flexDirection: "row",
            alignItems: "center",
            paddingTop: Device.isIphoneX ? 50 : 30
          },
          style
        ]}
      >
        <TouchableOpacity
          onPress={() => onClose("")}
          style={{
            width: 24,
            height: 24
          }}
        >
          <Image
            source={Images.close}
            style={{ width: 20, height: 20, resizeMode: "contain" }}
          />
        </TouchableOpacity>
        <Text
          style={{
            color: "#222",
            fontSize: 20,
            paddingLeft: 30,
            fontFamily: Constants.fontFamily
          }}
          ellipsizeMode="tail"
          numberOfLines={1}
        >
          {title}
        </Text>
      </View>
    );
  }
}
