import { connect } from "react-redux";
import LoginScreen from "../../../components/screens/login";
import {
  doLogin
} from "../../actions/authorize/login.action";

const mapStateToProps = state => {
  return {
    loginResponse: state.login,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    _doLogin: obj => dispatch(doLogin(obj)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(LoginScreen);
