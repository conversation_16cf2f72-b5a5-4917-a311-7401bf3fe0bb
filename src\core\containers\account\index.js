import { connect } from "react-redux";
import AccountScreen from "../../../components/screens/account";
import { emptyLogin } from "../../../core/actions/authorize/login.action";
import { removeRedeem } from "../../../core/actions/cart/cart.action";
import { setReturnRoute } from "../../../core/actions/cart/cart.action";

const mapStateToProps = state => {
  return {
    loggedUser: state.login.data,
    configSite: state.home.configSite,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    _emptyLogin: () => {
      dispatch(emptyLogin());
      dispatch(removeRedeem());
    },
    _setReturnRoute: route => {
      dispatch(setReturnRoute(route));
    }
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(AccountScreen);
