import {
  EVENT_LIST_REQUEST,
  EVENT_LIST_SUCCESS,
  EVENT_LIST_FAILURE,
} from "../../actions/types.action";

import AsyncStorage from '@react-native-async-storage/async-storage';
import services from "../../services/modules/events.service";
import { put, call, takeLatest } from "redux-saga/effects";

function* fetDataTask(obj) {
  try {
    const token = yield AsyncStorage.getItem("ACCESS_TOKEN");
    console.log('fetach data Task')
    console.log(obj.payload)
    const res = yield call(services.fetchDataEvents, obj.payload, token);
    if ((res.status === 200 || res.status === 201) && res.data !== null) {
      yield put({
        type: EVENT_LIST_SUCCESS,
        payload: res.data.data
      });
    } else {
      yield put({
        type: EVENT_LIST_FAILURE,
        payload: res.data
      });
    }
  } catch (error) {
    yield put({
      type: EVENT_LIST_FAILURE,
      payload: error
    });
  }
}

export function* watchEvents() {
  yield takeLatest(EVENT_LIST_REQUEST, fetDataTask);
}
