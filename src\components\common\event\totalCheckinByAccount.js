import React, { PureComponent } from "react";
import { View, Text, StyleSheet } from "react-native";
import { Images, Constants, Color, Styles } from "@common";

import HeadingTwo from "@share/Heading/HeadingTwo"
import RowInfoTwo from "@share/RowInfo/RowInfoTwo";

export default function TotalCheckinByAccount(props){
  return(
    <View style={Styles.sectionWrapperPTop}>
      <HeadingTwo title="TOTAL CHECKED IN BY ACCOUNT" />
      <RowInfoTwo label="Nhân viên 001" value="456" total="500"/>
      <RowInfoTwo label="Nhân viên 002" value="456" total="500"/>
      <RowInfoTwo label="Nhân viên 003" value="456" total="500"/>
      <RowInfoTwo label="Nhân viên 004" value="456" total="500"/>
      <RowInfoTwo label="Nhân viên 005" value="456" total="500"/>
    </View>
  );
}
