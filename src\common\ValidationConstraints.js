const validationConstraints = {
  //Login form
  email: {
    email: {
      message: "^Địa chỉ email không hợp lệ"
    },
    presence: {
      message: "^Vui lòng nhập email"
    }
  },

  password: {
    presence: {
      message: "^Vui lòng nhập password"
    }
  },

  //signup form
  username: {
    presence: {
      message: "^Vui lòng nhập họ tên"
    }
  },
  rememberName: {
    presence: {
      message: "^Vui lòng nhập tên gợi nhớ"
    }
  },
  fName: {
    presence: {
      message: "^Vui lòng nhập họ"
    }
  },
  lName: {
    presence: {
      message: "^Vui lòng nhập tên"
    }
  },
  numberphone: {
    presence: {
      message: "^Vui lòng nhập số điện thoại"
    }
  },
  city: {
    presence: {
      message: "^Vui lòng chọn tỉnh/ thành phố"
    }
  },
  district: {
    presence: {
      message: "^Vui lòng chọn quận/ huyện"
    }
  },
  address: {
    presence: {
      message: "^<PERSON>ui lòng nhập địa chỉ"
    }
  },
  message: {
    presence: {
      message: "^Vui lòng nhập tin nhắn"
    }
  },
  firstname: {
    presence: {
      message: "^Vui lòng nhập Họ"
    },
    length: {
      minimum: 1,
      message: "^Họ tối thiểu 1 ký tự"
    }
  },
  lastname: {
    presence: {
      message: "^Vui lòng nhập Tên"
    },
    length: {
      minimum: 1,
      message: "^Tên tối thiểu 1 ký tự"
    }
  },
  confirmPassword: {
    presence: {
      message: "^Vui lòng nhập password xác nhận"
    },
    length: {
      minimum: 6,
      message: "^Password tối thiểu 6 ký tự"
    }
  },
  productName: {
    presence: {
      message: "^Vui lòng chọn 1 sản phẩm"
    }
  }
};
export default validationConstraints;
