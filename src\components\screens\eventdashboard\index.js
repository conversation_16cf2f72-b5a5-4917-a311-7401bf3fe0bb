import React, { useState, useEffect } from "react";
import {
  Text,
  FlatList,
  Dimensions,
  TouchableOpacity,
  View,
  SafeAreaView,
  ScrollView
} from "react-native";
import {Styles, Constants} from "@common";
import styles from './styles';
;
import HeadingText from "@share/Heading/HeadingText"

const windowW = Dimensions.get("window").width;

function EventDashboard(props) {
    const [events, setEvents] = useState([]);
    const [accessToken, setAccessToken] = useState("");
    const [activeTab, setActiveTab] = useState("ongoing");
    const [loadMore, setLoadMore] = useState(false);

    const onSelectEvent = ({item}) => {
        console.log(item);
    }

    return (
        <SafeAreaView
            style={Styles.containerView}
            >
            <ScrollView style={Styles.scrollViewWrapper}>
                <HeadingText title="P1HARMONY LIVE TOUR [P1USTAGE H:P1ONEER] IN BANGKOK"/>
                <View style={styles.totalViewWrapper}>
                    
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

export default EventDashboard;