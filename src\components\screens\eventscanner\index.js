import React, { useCallback, useState, useRef, useEffect } from "react";
import {
  Text,
  Dimensions,
  TouchableOpacity,
  View,
  SafeAreaView,
  StyleSheet,
  Alert,
  ActivityIndicator
} from "react-native";
import {Styles, Constants, Color} from "@common";
import styles from './styles';
import HeadingText from "@share/Heading/HeadingText"
import Ionicons from 'react-native-vector-icons/Ionicons'
import QRCodeScanner from 'react-native-qrcode-scanner';
import { RNCamera } from 'react-native-camera';
import { useFocusEffect } from '@react-navigation/native';

const windowW = Dimensions.get("window").width;

function EventScanner(props) {
  const [isLoading, setLoading] = useState(false);
  const item = props.eventscanner.selectedEvent;
  
  const onCheckIn = (data) => {
    console.log('VIEW QR CODE')
    console.log(isLoading);
    if(!isLoading){
      let nft_code = data.split("/");
      if(nft_code.length > 5){
        console.log(nft_code);
        let data_code = nft_code[4];
        let filterObj = {
          event_date_id: item.id,
          qr_nft: data_code
        }
        console.log(filterObj)
        setLoading(true);
        props.checkNft(filterObj);
      }else{
        Alert.alert(
          "Thông báo",
          "Mã QR không tồn tại",
          [
            {
              text: "Ok",
              onPress: () => {
                props.emptyTicket();
                setLoading(false);
              }
            }
          ]
        );
      }
    }
  }

  useFocusEffect(
    useCallback(() => {
      if(props.eventscanner){
        console.log("====================11111")
        console.log(props.eventscanner.data.message)
        
        console.log(props.eventscanner.data.message != 'OK');
        console.log(props.eventscanner.data.message != undefined);
        console.log("====================11111")
        if(props.eventscanner.data.message != undefined){
          console.log(props.eventscanner.data.message);
          if(props.eventscanner.data.message == 'QR_CHECKED_BEFORE'){
            Alert.alert(
              "Thông báo",
              "Vé này đã CHECK IN trước đó",
              [
                {
                  text: "Ok",
                  onPress: () => {
                    props.emptyTicket();
                    setLoading(false);
                  }
                }
              ]
            );
          }
          if(props.eventscanner.data.message == 'QR_NOT_EXISTS'){
            Alert.alert(
              "Thông báo",
              "Mã QR không tồn tại",
              [
                {
                  text: "Ok",
                  onPress: () => {
                    props.emptyTicket();
                    setLoading(false);
                  }
                }
              ]
            );
          }
        }else{
          console.log("====================22222")
          console.log(props.eventscanner.data)
          console.log("====================2222")
          if(Object.keys(props.eventscanner.data).length !== 0){
            setLoading(false);
            console.log('SCREEN HERE')
            props.navigation.navigate("TicketDetail", {
              item: props.eventscanner
            });
          }
        }
      }
    }, [props.eventscanner, isLoading])
  )

  const toggleFlash = () => {

    props.emptyTicket();
  }

  const renderDetectorContent = () => {
    return (
      <View style={styles.camView}>
        <QRCodeScanner
          onRead={({data}) => onCheckIn(data)}
          reactivate={!isLoading}
          reactivateTimeout={3000}
          showMarker={true}
          cameraStyle={styles.camStyle}
        />
      </View>
    );
  };

  return (
    <SafeAreaView
        style={Styles.containerView}
        >
        <View style={Styles.scrollViewWrapper}>
            <HeadingText title={item.event_stage.event.name_vi}/>
            <View style={styles.subWrap}>
                <Text style={styles.txtSub}>Camera at QR code</Text>
            </View>
            <View style={[styles.camWrap, Styles.Common.RowCenter, {overflow: 'hidden'}]}>
              {renderDetectorContent()}
            </View>
            <View style={[styles.viewFlashWrap, Styles.Common.ColumnCenter]}>
                <TouchableOpacity 
                    onPress={() => toggleFlash()}
                    style={Styles.Common.ColumnCenter}
                >
                    <Ionicons name="flash-outline" size={20} color={Color.secondary} />
                    <Text style={styles.txtFlash}>Flash On</Text>
                </TouchableOpacity>
            </View>
        </View>
    </SafeAreaView>
  );
}

export default EventScanner;