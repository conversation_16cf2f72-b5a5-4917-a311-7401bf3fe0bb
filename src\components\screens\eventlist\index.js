import React, { useState, useEffect } from "react";
import {
  Text,
  FlatList,
  Dimensions,
  TouchableOpacity,
  View,
  SafeAreaView,
  Platform,
  ActivityIndicator,
  RefreshControl
} from "react-native";
import {Styles, Constants} from "@common";
import styles from './styles';
import _ from "lodash";

import HeadingText from "@share/Heading/HeadingText"
import EventItem from "@commonComponent/eventItem"
import EmptyList from "@commonComponent/emptyList";

const windowW = Dimensions.get("window").width;
const shoppingList = [
    { id: 1, item: "A" },
    { id: 2, item: "B" },
    // { id: 3, item: "C" },
  ];

function EventListScreen(props) {
    const [events, setEvents] = useState([]);
    const [accessToken, setAccessToken] = useState("");
    const [activeTab, setActiveTab] = useState("ongoing");
    const [loadMore, setLoadMore] = useState(false);

    const onSelectEvent = (item) => {
        if(item.allow_check_in == 2){
            // set event
            props.selectEventDetail(item);
            // navigate dashboard event detail
            if(props.loggedUser.customer.role == 'organizer'){

                props.navigation.navigate("AppDrawerOrg", 
                    {
                        screen: 'Dashboard',
                        params: { 
                            item: item, 
                        },
                    }
                )
            }else{
                props.navigation.navigate("AppDrawer", 
                    {
                        screen: 'Scanner',
                        params: { 
                            item: item, 
                        },
                    }
                )
            }
        }
    }
    
    const renderItem = ({item}) => (
        <EventItem item={item} key={item.id} onPress={() => onSelectEvent(item)}/>
    );
    
    const renderEmpty = () => {
        return (
            <EmptyList />
        );
    }

    const _loadmore = () => {

    }

    const _onRefresh = () => {
        console.log('On Refresh')
        getData();
    }

    useEffect(() => {
        setEvents(props.events.data)
    }, [props.events]);

    useEffect(() => {
        getData()
    }, [activeTab]);

    const getData = () => {
        let filter_type = 1;
        if(activeTab == 'ongoing') filter_type = 1;
        if(activeTab == 'upcomming') filter_type = 2;
        if(activeTab == 'happened') filter_type = 3;
        let filterObj = {
            type: filter_type,
        }
        props.getEvents(filterObj);
    }

    return (
        <View style={{flex: 1, padding: Constants.paddingScreen}}>
            <HeadingText title="EVENT"/>
            <View style={[Styles.Common.RowCenterBetween, styles.tabButtonWrapper]}>
                <TouchableOpacity onPress={() => setActiveTab('ongoing')} style={[styles.tabButton, (activeTab == 'ongoing') && styles.tabButtonActive]}>
                    <Text style={styles.txtTabButton}>Đang diễn ra</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => setActiveTab('upcomming')} style={[styles.tabButton, (activeTab == 'upcomming') && styles.tabButtonActive]}>
                    <Text style={styles.txtTabButton}>Sắp diễn ra</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => setActiveTab('happened')} style={[styles.tabButton, (activeTab == 'happened') && styles.tabButtonActive]}>
                    <Text style={styles.txtTabButton}>Đã diễn ra</Text>
                </TouchableOpacity>
            </View>
            <SafeAreaView style={styles.listWrapper}>
                <FlatList
                    refreshControl={
                        <RefreshControl
                            refreshing={loadMore}
                            onRefresh={_onRefresh.bind(this)}
                        />
                    }
                    style={{flex: 1, width: '100%'}}
                    contentContainerStyle={styles.listView}
                    initialNumToRender={Constants.pagingLimit}
                    data={events}
                    renderItem={renderItem}
                    keyExtractor={item => item.id}
                    onEndReachedThreshold={1}
                    ListEmptyComponent={renderEmpty}
                    onEndReached={_loadmore}
                    ListFooterComponent={
                        loadMore && shoppingList.length > 0 && 
                        <ActivityIndicator
                            size="small"
                            style={{paddingVertical: 20}}
                        />
                    }
                />
            </SafeAreaView>
        </View>
    );
}

export default EventListScreen;