/**
 * Created by InspireUI on 20/12/2016.
 *
 * @format
 */

import { Dimensions } from "react-native";

const { width, height } = Dimensions.get("window");

const Constants = {
  fontFamily: "Montserrat-Regular",
  fontFamilyBold: "Montserrat-Bold",
  // fontFamilyBold: "elle futura bold",
  fontHeader: "Montserrat-Regular",

  Dimension: {
    ScreenWidth(percent = 1) {
      return Dimensions.get("window").width * percent;
    },
    ScreenHeight(percent = 1) {
      return Dimensions.get("window").height * percent;
    }
  },
  LimitAddToCart: 10,
  TagIdForProductsInMainCategory: 263,
  Window: {
    width,
    height,
    headerHeight: (65 * height) / 100,
    headerBannerAndroid: (55 * height) / 100,
    profileHeight: (45 * height) / 100
  },
  paddingScreen: 15,
  pagingLimit: 10,
  fontText: {
    size: 16
  },
};

export default Constants;
