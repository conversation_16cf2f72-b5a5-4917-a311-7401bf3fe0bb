import React, { PureComponent } from "react";
import { View, Text } from "react-native";
import { Constants, Color, Styles } from "@common";
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons'

export default class ErrorMessage extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    let { message } = this.props;
    if (message && message !== "") {
      return (
        <View style={{flexDirection:'row', alignItems:'center', justifyContent: 'flex-start', width: '100%'}}>
          <MaterialCommunityIcons
            name="information"
            size={16}
            color={Color.error}
             style={{marginRight: 5}}
          />
          <Text
            style={{ flex: 9, color: Color.error, fontFamily: Constants.fontFamily, marginLeft:5, fontSize: 12 }}
          >
            {message}
          </Text>
        </View>
      );
    }
    return null;
  }
}
