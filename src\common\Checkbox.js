import React, { Component } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import Feather from 'react-native-vector-icons/Feather';

import Color from "./Color";
import Constants from "./Constants";

export default class Checkbox extends Component {
  render() {
    var { onSelect, value, selectedValue, text, currentVal } = this.props;
    return (
      <TouchableOpacity onPress={() => onSelect(value, text)}>
        <View style={[styles.flexRow]}>
          <View
            style={[
              styles.checkbox,
              value === selectedValue || text === currentVal
                ? styles.checkboxActive
                : ""
            ]}
          >
            <Feather name="check" size={18} color="#fff" />
          </View>
          <View style={styles.listItem}>
            <Text style={styles.itemCaption}>{text}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderColor: "#ddd",
    borderWidth: 1,
    alignItems: "center",
    justifyContent: "center"
  },
  checkboxActive: {
    backgroundColor: Color.primary,
    borderColor: Color.primary
  },
  flexRow: { flexDirection: "row", alignItems: "center" },
  listItem: {
    marginLeft: 15,
    position: "relative",
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    flex: 1
  },
  itemCaption: { fontFamily: Constants.fontFamily }
});
