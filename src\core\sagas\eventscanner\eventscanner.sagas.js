import {
  EVENT_SCANNER_REQUEST,
  EVENT_SCANNER_SUCCESS,
  EVENT_SCANNER_FAILURE,

  TICKET_CHECKIN_REQUEST,
  TICKET_CHECKIN_SUCCESS,
  TICKET_CHECKIN_FAILURE,

} from "../../actions/types.action";
  
import AsyncStorage from '@react-native-async-storage/async-storage';
import services from "../../services/modules/eventscanner.service";
import { put, call, takeLatest } from "redux-saga/effects";

function* fetDataDetailTask(obj) {
  try {
    const token = yield AsyncStorage.getItem("ACCESS_TOKEN");
    const res = yield call(services.fetchData, obj.payload, token);
    if ((res.status === 200 || res.status === 201) && res.data !== null) {
      // console.log('================')
      // console.log(res.data)
      // console.log('================')
      yield put({
        type: EVENT_SCANNER_SUCCESS,
        payload: (res.data.data) ? res.data.data : res.data
      });
    } else {
      yield put({
        type: EVENT_SCANNER_FAILURE,
        payload: res.data
      });
    }
  } catch (error) {
    yield put({
      type: EVENT_SCANNER_FAILURE,
      payload: error
    });
  }
}

function* fetCheckinTask(obj) {
  try {
    const token = yield AsyncStorage.getItem("ACCESS_TOKEN");
    const res = yield call(services.fetchCheckinData, obj.payload, token);
    if ((res.status === 200 || res.status === 201) && res.data !== null) {
      // console.log('================')
      // console.log(res.data)
      // console.log('================')
      yield put({
        type: EVENT_SCANNER_SUCCESS,
        payload: (res.data.data) ? res.data.data : res.data
      });
    } else {
      yield put({
        type: EVENT_SCANNER_FAILURE,
        payload: res.data
      });
    }
  } catch (error) {
    yield put({
      type: EVENT_SCANNER_FAILURE,
      payload: error
    });
  }
}

export function* watchEventScanner() {
  yield takeLatest(EVENT_SCANNER_REQUEST, fetDataDetailTask);
  yield takeLatest(TICKET_CHECKIN_REQUEST, fetCheckinTask);
}
  