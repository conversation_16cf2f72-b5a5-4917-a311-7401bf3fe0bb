import React, { useCallback, useState, useRef, useEffect } from "react";
import {
  Text,
  Dimensions,
  TouchableOpacity,
  View,
  SafeAreaView,
  ScrollView,
  BackHandler,
  Alert
} from "react-native";
import moment from 'moment'
import {Styles, Constants, Color} from "@common";
import styles from './styles';
import HeadingText from "@share/Heading/HeadingText"
import Ionicons from 'react-native-vector-icons/Ionicons'
import HeadingTwo from '@share/Heading/HeadingTwo'
import RowInfo from '@share/RowInfo/RowInfo'
import {Spinner, ButtonNormal, Button} from "@share";

const windowW = Dimensions.get("window").width;

function TicketDoorlistDetail(props) {
    const [isLoading, setLoading] = useState(false);
    console.log(props.eventscanner.data);

    const onCloseModal = () => {
      props.emptyTicket();
        if(props.navigation.canGoBack()){
          props.navigation.goBack()
        }else{
            props.navigation.navigate('AppDrawerNavigator', {
                screen: 'EventListScreen'
            })
        }
    }

    useEffect(() => {
        if(props.eventscanner){
          // console.log("EVENT SCANNER")
          // console.log(props.eventscanner)
          // console.log('SCANNER CHANGES')
          if(props.eventscanner.data && props.eventscanner.data.message != 'OK'){
            if(props.eventscanner.data.message == 'TICKET_CHECKED_BEFORE'){
              Alert.alert(
                "Thông báo",
                "Vé này đã CHECK IN trước đó",
                [
                  {
                    text: "Ok",
                    onPress: () => {
                      props.emptyTicket();
                      setLoading(false);
                      onCloseModal();
                    }
                  }
                ]
              );
            }
            if(props.eventscanner.data && props.eventscanner.data.message == 'TICKET_NOT_EXISTS'){
              Alert.alert(
                "Thông báo",
                "Mã QR không tồn tại",
                [
                  {
                    text: "Ok",
                    onPress: () => {
                      props.emptyTicket();
                      setLoading(false);
                      onCloseModal();
                    }
                  }
                ]
              );
            }
          }else{
            setLoading(false);
          }
        }
      }, [props.eventscanner, isLoading])

    const onCheckedTicket = () => {
        if(!isLoading){
            setLoading(true);
            let obj = {
                id: props.eventscanner.data.id,
                event_date_id: props.eventscanner.data.cart_temp.event_date_id
            }
            props.checkinTicket(obj)
        }
    }

    const show_round = props.eventscanner.data.event_date ? moment(props.eventscanner.data.event_date.show_date * 1000).format('ddd D MMM YYYY HH:mm') : '';

    return ( props.eventscanner.data.event && 
        <SafeAreaView
            style={Styles.containerView}
            >
            <ScrollView style={Styles.scrollViewWrapper} contentContainerStyle={{paddingVertical: Constants.paddingScreen}}>
                <HeadingText title={props.eventscanner.data.event.meta_title_vi}/>
                {
                    (props.eventscanner.data && props.eventscanner.data.checked_time) && 
                    <View style={[styles.validWrap, Styles.Common.ColumnCenter]}>
                        <Text style={styles.txtValid}>Valid Ticket</Text>
                        <Ionicons name="checkmark-circle" size={80} color={Color.success} /> 
                    </View>
                }
                <HeadingTwo title="BUYER INFOMATION" />
                <RowInfo label="Name:" value={props.eventscanner.data.name}/>
                <RowInfo label="NFT Code:" value={props.eventscanner.data.mint_address}/>
                <RowInfo label="Phone Number:" value={props.eventscanner.data.cart_temp.phone}/>
                <RowInfo label="Email:" value={props.eventscanner.data.cart_temp.email}/>
                <HeadingTwo title="TICKET DETAILS" />
                <RowInfo label="Order Number:" value={props.eventscanner.data.cart_temp.timer_ticket}/>
                <RowInfo label="Ticket Type:" value=""/>
                <RowInfo label="Show Round:" value={show_round}/>
                <RowInfo label="Zone:" value={props.eventscanner.data.zone_name}/>
                <RowInfo label="Seat Number:" value={props.eventscanner.data.seat_name}/>
            </ScrollView>
            <View style={[{padding: Constants.paddingScreen}, Styles.Common.RowCenterBetween]}>
                <ButtonNormal
                    text="Close"
                    containerStyle={{flex: 1, marginHorizontal: 4, borderRadius: 22, backgroundColor: Color.blue}}
                    onPress={() => onCloseModal()}
                />
                {
                    (!props.eventscanner.data.checked_time) &&
                        <ButtonNormal
                            disabled={isLoading}
                            text="Check-in"
                            containerStyle={{flex: 1, marginHorizontal: 4, borderRadius: 22, backgroundColor: Color.primary}}
                            onPress={() => onCheckedTicket()}
                        />
                }
            </View>
        </SafeAreaView>
    );
}

export default TicketDoorlistDetail;