import React, { useState, useEffect } from "react";
import {
  Text,
  View,
  StyleSheet,
  Platform
} from "react-native";
import { Styles, Color, Constants } from "@common";

function RowInfoThree(props) {

  return (
    <View style={[Styles.rowWrapper, Styles.Common.RowCenterBetween]}>
      <View style={styles.wrapperLeft}>
        <Text style={styles.txtLabel}>{props.label}</Text>
      </View>
      <View style={[styles.wrapperRight, Styles.Common.RowCenterBetween]}>
        <View style={styles.totalTicket}>
            <View style={[styles.currentTicket, {width: props.total? props.value*100/props.total + '%'  : '0%'}]}></View>
        </View>
        <Text style={styles.txtValue}>{props.revenue}</Text>
      </View>
    </View>
  );
}

export default RowInfoThree;

const styles = StyleSheet.create({
  wrapperLeft: {
    flex: 1,
  },
  wrapperRight: {
    flex: 2,
  },
  txtLabel:{
    fontSize: Platform.OS == 'ios' ? 14 : 10,
    fontFamily: Constants.fontFamilyBold,
    color: Color.blue
  },
  txtValue: {
    fontSize: Platform.OS == 'ios' ? 14 : 10,
    fontFamily: Constants.fontFamily,
    color: Color.primary,
    paddingLeft: 6,
    textAlign: 'center'
  },
  totalTicket: {
    flex: 2,
    height: 10,
    width: '100%',
    backgroundColor: Color.subColor
  },
  currentTicket: {
    height: 10,
    backgroundColor: Color.primary
  }
});