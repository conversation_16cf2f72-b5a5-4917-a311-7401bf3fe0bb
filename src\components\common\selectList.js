import React, { PureComponent } from "react";
import { View, Text, TouchableOpacity, Modal, ScrollView, LayoutAnimation, Platform } from "react-native";
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons'
import { Styles, Constants } from "@common";
import styles from "./listStyle";
import Feather from 'react-native-vector-icons/Feather'
import { HeaderTitle } from "react-navigation-stack";

export default class SelectList extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
    };
  }

  componentDidMount() {
    let data = this.props.data;
    this.setState({
      data: data,
    });
  }

  render() {
    var { value, onClose, onSelect, visible, title } = this.props;
    return (
    <View style={{flex: 1}}>
      <Modal transparent={true}
              visible={visible}>
        <View
          style={{
            backgroundColor: "rgba(0,0,0,.5)",
            alignItems: "center",
            justifyContent: "center",
            flex: 1,
          }}
        >
          <View style={styles.container}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                paddingVertical: 10,
                paddingLeft: 15,
                backgroundColor: "#fff",
              }}
            >
              <Text style={{ fontWeight: "bold" }}>{title.toUpperCase()}</Text>
              <TouchableOpacity onPress={() => onClose()}>
                <Feather
                  name="x"
                  size={18}
                  style={{ marginRight: 10 }}
                  color="#999"
                />
              </TouchableOpacity>
            </View>
            <ScrollView
              style={styles.listItems}
              contentContainerStyles={{ padding: 15 }}
            >
              {this.state.data.map((e, index) => {
                return (
                  <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => onSelect(e)}
                    key={index.toString()}
                  >
                    <View style={styles.listItem}>
                      <Text>{e}</Text>
                      {value === e && (
                        <Feather name="check" size={18} color="green" />
                      )}
                    </View>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
    );
  }
}
