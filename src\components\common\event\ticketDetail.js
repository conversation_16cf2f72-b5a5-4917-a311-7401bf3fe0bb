import React, { PureComponent, useEffect } from "react";
import { View, Text, StyleSheet, Platform} from "react-native";
import { Images, Constants, Color, Styles } from "@common";

import HeadingTwo from "@share/Heading/HeadingTwo"
import RowInfoThree from "@share/RowInfo/RowInfoThree";

export default function TicketDetail(props){

  useEffect(() => {

  }, [props.data])

  return(
    <View >
      <HeadingTwo title="DETAILS" />

      <View style={[Styles.rowWrapper, Styles.Common.RowCenterBetween]}>
        <View style={styles.wrapperLeft}>
        </View>
        <View style={[styles.wrapperRight, Styles.Common.RowCenterBetween]}>
          <View style={[styles.totalTicket, {backgroundColor: 'none', height: 'auto'}]}>
            <Text style={[styles.txtTh, {paddingLeft: 0}]}>TICKET SALES</Text>
          </View>
          <Text style={styles.txtTh}>REVENUE</Text>
        </View>
      </View>

      {
        props.data && props.data.map((zones, index) => {
          return zones.seats.map((seat, i) => { 
            return (
              <RowInfoThree key={index + i} label={"Zone: " + zones.zone_name + " - Row: " + seat.row_code} value={seat.totalCheckin ? seat.totalCheckin : 0} total={seat.totalTickets ? seat.totalTickets : 0} revenue={seat.totalPrices ? seat.totalPrices.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) : 0}/>
            );
          })
        })
      }
    </View>
  );
}


const styles = StyleSheet.create({
  wrapperLeft: {
    flex: 1,
  },
  wrapperRight: {
    flex: 2,
  },
  txtValue: {
    fontSize: 10,
    fontFamily: Constants.fontFamily,
    color: Color.primary,
    paddingLeft: 6,
    textAlign: 'center'
  },
  txtTh: {
    fontSize: Platform.OS == 'ios' ? 12 : 10,
    fontFamily: Constants.fontFamily,
    color: Color.secondary,
    paddingLeft: 6,
    textAlign: 'center'
  },
  totalTicket: {
    flex: 2,
    height: 10,
    width: '100%',
    backgroundColor: Color.subColor
  },
  currentTicket: {
    height: 10,
    width: '30%',
    backgroundColor: Color.primary
  }
});