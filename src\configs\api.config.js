// const API_DEV = require("./environments/dev").ENV_DEV;
const API_DEV = require("./environments/prod").ENV_DEV;
export const ApiConfig = {
  App: {
    Events: {
      GetEvents: `${API_DEV.APP_API_URL}/organizer/events?type={{TYPE}}`,
      CheckQRCODE: `${API_DEV.APP_API_URL}/organizer/qr_check`,
      dootlistCheckIn: `${API_DEV.APP_API_URL}/organizer/doorlist_checkin`,
      Dashboard: `${API_DEV.APP_API_URL}/organizer/dashboard?event_date_id={{event_date_id}}`,
    },
    Doorlist: {
      GetDoorlist: `${API_DEV.APP_API_URL}/organizer/doorlist?event_date_id={{event_date_id}}`,
    },
    Authorization: {
      Login: `${API_DEV.APP_API_URL}/organizer/login`,
    },
    Customer: {
      AccountInfo: `${API_DEV.APP_URL}/getAccountInfo`,
    },
  }
};
