import React, { PureComponent } from "react";
import { View, Text, StyleSheet, Platform } from "react-native";
import { Images, Constants, Color, Styles } from "@common";

import HeadingTwo from "@share/Heading/HeadingTwo"

export default function TicketSales (props){
  return(
    <View style={Styles.sectionWrapperPTop}>
      <HeadingTwo title="TICKET SALES" />
      <View style={[Styles.Common.RowCenterBetween, styles.wrapperView]}>
        <View style={[Styles.Common.ColumnCenter, styles.viewContent]}>
          <Text style={styles.txtLabel}>ISSUED TICKET</Text>
          <Text style={styles.txtValue}>{props.total ? props.total : 0}</Text>
        </View>
        <View style={[styles.borderLeft, styles.borderRight, Styles.Common.ColumnCenter, styles.viewContent]}>
          <Text style={styles.txtLabel}>SOLD TICKET</Text>
          <Text style={styles.txtValue}>{props.sold ? props.sold : 0}</Text>
        </View>
        <View style={[Styles.Common.ColumnCenter, styles.viewContent]}>
          <Text style={styles.txtLabel}>REVENUE</Text>
          <Text style={styles.txtValue}>{props.revenue ? props.revenue.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) : 0}</Text>
        </View>
      </View>
    </View>
  );
}


const styles = StyleSheet.create({
  wrapperView: {
    paddingVertical: 8,
    paddingHorizontal: 10,
  },
  txtLabel:{
    fontSize: Platform.OS == 'ios' ? 10 : 8,
    marginBottom: 6,
    fontFamily: Constants.fontFamily,
    color: Color.secondary
  },
  txtValue: {
    fontSize: 15,
    fontFamily: Constants.fontFamilyBold,
    color: Color.primary
  },
  borderLeft:{
    borderLeftWidth: 1,
    borderLeftColor: Color.subColor
  },
  borderRight:{
    borderRightWidth: 1,
    borderRightColor: Color.subColor
  },
  viewContent: {
    flex: 1
  }
});