import React, { Component } from "react";
import { View, Text, ScrollView, Image, StyleSheet } from "react-native";
import { Constants } from "@common";
import ProductView from "./productView";
import TopProductView from "./productView";
import styles from "./listItemStyles";
import { WaitingLoader } from "@common";

export default class HorizontalList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      products: [],
      isLoading: true
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.setState({
      products: nextProps.products ? nextProps.products : [],
      isLoading: false
    });
  }

  componentDidMount() {
    this.setState({
      products: this.props.products ? this.props.products : [],
      isLoading: false
    });

  }

  render() {
    if (this.state.isLoading) return <WaitingLoader />;
    return (
      <ScrollView horizontal={true} style={internalStyles.listProducts}>
        {this.props.isTopProductList
          ? this.state.products.map((product, idx) => (
              <TopProductView
                key={product.id + "---" + idx}
                navigation={this.props.navigation}
                product={product}
                type={"HorizontalList"}
                returnRoute={this.props.form}
              />
            ))
          : this.props.isHistoryList
          ? this.state.products.map((product, idx) => (
              <ProductView
                key={product.id + "---" + idx}
                navigation={this.props.navigation}
                product={product}
                type={"HorizontalList"}
                isHistoryList={true}
                langId={this.props.langId}
                returnRoute={this.props.form}
              />
            ))
          : this.state.products.map((product, idx) => (
              <ProductView
                key={product.id + "---" + idx}
                navigation={this.props.navigation}
                product={product}
                type={"HorizontalList"}
                returnRoute={this.props.form}
              />
            ))}
      </ScrollView>
    );
  }
}

const internalStyles = StyleSheet.create({
  listProducts: {
    marginLeft: 15,
    marginBottom: 30,
    paddingBottom: 10
  }
});
