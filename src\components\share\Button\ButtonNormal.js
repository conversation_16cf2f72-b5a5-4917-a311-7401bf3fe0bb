/**
 * Created by InspireUI on 17/02/2017.
 *
 * @format
 */

import React, { PureComponent } from "react";
import { TouchableOpacity, Text, StyleSheet, View, Platform } from "react-native";
import { Color, Constants } from "@common";
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons'

class But<PERSON> extends PureComponent {
    render() {
        const {
            text,
            icon,
            onPress,
            button,
            containerStyle,
            textStyle,
            containerColor,
            textColor,
            disabled,
        } = this.props;
        return (
        <TouchableOpacity
            disabled={disabled ? disabled : false}
            style={[
                button,
                containerStyle,
            ]}
            onPress={onPress}>

            <View style={styles.linearGradient}>
                { icon 
                    ? ( <MaterialCommunityIcons name={icon} color={textColor} size={24} style={styles.icon} /> ) 
                    : ( <View /> )
                }
                <Text style={[styles.text, { color: textColor }, textStyle]}>
                    {text}
                </Text>
            </View>
        </TouchableOpacity>
        );
    }
    }

    const styles = StyleSheet.create({
    text: {
        fontFamily: Constants.fontFamilyBold,
        fontSize: 15
    },
    icon: {
        marginRight: 10,
    },
    linearGradient: {
        
        minHeight:44,
        justifyContent: "center",
        alignItems: "center",
        paddingHorizontal: 10,
        flexDirection: "row",
    }
});

Button.defaultProps = {
    text: "Button",
    onPress: () => "Button pressed!",
    containerStyle: {},
    textStyle: {},
    containerColor: Color.theme2,
    textColor: "white",
};

export default Button;
