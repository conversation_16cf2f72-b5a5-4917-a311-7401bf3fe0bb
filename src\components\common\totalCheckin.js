import React, {PureComponent} from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Images, Constants, Device, Styles } from "@common";
import Entypo from 'react-native-vector-icons/Entypo';


const styles = StyleSheet.create({
  viewWrap: {
    paddingVertical: 30,
  },

});

const TotalCheckin = ({item}) => {
  return (
    <View style={styles.viewWrap}>
        <Text >TOTAL CHECKIN</Text>
    </View>
  );
}

export default TotalCheckin;