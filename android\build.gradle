// Top-level build file where you can add configuration options common to all sub-projects/modules.
subprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            project.android {
                if (!android.hasProperty('namespace') || namespace == null) {
                    def manifestFile = file("${project.projectDir}/src/main/AndroidManifest.xml")
                    if (manifestFile.exists()) {
                        def manifestContent = new XmlParser().parse(manifestFile)
                        def packageName = manifestContent.@package
                        if (packageName) {
                            namespace = packageName
                            println "Set namespace for ${project.name} to ${packageName}"
                        } else {
                            namespace project.group
                            println "No package attribute found in AndroidManifest.xml for ${project.name}"
                        }
                    } else {
                        namespace project.group
                        println "No AndroidManifest.xml found for ${project.name}"
                    }
                }
            }
        }
    }
}

buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 21
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.5.2")
        classpath("com.facebook.react:react-native-gradle-plugin")
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
