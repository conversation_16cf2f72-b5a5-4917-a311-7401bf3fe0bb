import React, { useState } from "react";
import { Text, View, Image, StyleSheet } from "react-native";
import { connect } from "react-redux";
import { Constants, Images, Color, Styles } from "@common";
import Ionicons from 'react-native-vector-icons/Ionicons'
import Feather from "react-native-vector-icons/Feather"

function TabBarIcon(props) {
  const [unreadNotify, setUnreadNotify] = useState(0);

  renderIcon = (icon, focused) => {
    switch (icon) {
      case "home":
        return (
          <Feather size={20} color={props.focused ? '#fff' : Color.secondary} name="bar-chart-2" />
        );
      case "scanner":
        return (
          <Ionicons size={20} color={props.focused ? '#fff' : Color.secondary} name="scan" />
        );
      case "user":
        return (
          <Image
            source={Images.tabbarIcons.account}
            style={{
              width: 20,
              height: 22,
              resizeMode: "contain",
              opacity: focused ? 1 : 0.2
            }}
          />
        );
      case "bell":
        return (
          <Feather size={20} color={props.focused ? '#fff' : Color.secondary} name="users" />
        );
      case "mail":
        return (
          <Image
            source={Images.tabbarIcons.contact}
            style={{
              width: 23,
              height: 25,
              resizeMode: "contain",
              opacity: focused ? 1 : 0.2
            }}
          />
        );
    }
  };

  return(
    <View style={[{ position: "relative" }, Styles.Common.ColumnCenter]}>
      {this.renderIcon(props.image, props.focused)}
      <Text style={[styles.txtTitle, props.focused && styles.activeTab]}>{props.title}</Text>
    </View>
  );
}

const mapStateToProps = state => {
  return {
    cart: state.cart,
    // notifications: state.notifications.data,
    unreadNotify: state.unreadNotify
  };
};

const styles = StyleSheet.create({
  activeTab: {
    color: 'white'
  },
  badge: {
    borderRadius: 15,
    paddingHorizontal: 6,
    minWidth: 15,
    minHeight: 15,
    paddingVertical: 2,
    backgroundColor: "red",
    position: "absolute",
    right: -5,
    top: -3,
    zIndex: 1,
    justifyContent: "center",
    alignItems: "center"
  },
  badgeIcon: { color: "#fff", fontSize: 8, fontFamily: Constants.fontFamily },
  txtTitle: {
    fontSize: 12,
    color: Color.secondary,
    fontFamily: Color.fontFamily
  }
});

export default connect(mapStateToProps, null)(TabBarIcon);
