import { connect } from "react-redux";
import EventListScreen from "../../../components/screens/eventlist";
import {
  fetchEvents
} from "../../actions/eventlist/eventlist.actions";
import {
  selectEventDetail
} from "../../actions/eventscanner/eventscanner.actions"

const mapStateToProps = state => {
  return {
    events: state.events,
    loggedUser: state.login.data,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    getEvents: obj => dispatch(fetchEvents(obj)),
    selectEventDetail: item => dispatch(selectEventDetail(item))
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(EventListScreen);
