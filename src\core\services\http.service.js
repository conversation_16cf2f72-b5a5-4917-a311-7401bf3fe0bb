import axios from "axios";
import { requestService } from "../services/request.service";
import { EnvConfig } from "../../configs/base.config";

class HttpService {
  _get(url, contentType) {
    return new Promise(async (resolve, reject) => {
      let token_key = this.checkDomain(url);
      console.log( "URL: " + url);
      let header = await requestService.getAuthHeaders(contentType, token_key);
      axios
        .get(url, header)
        .then(res => {
          resolve(res);
        })
        .catch(error => {
          reject(this.handleError(error));
        });
    });
  }
  _get2(url, contentType) {
    return new Promise(async (resolve, reject) => {
      // let token_key = this.checkDomain(url);
      // let header = await requestService.getAuthHeaders(contentType, token_key);
      let header = {
        headers: {
         // "Content-Type": "application/json",
          Authorization: "Basic RktSNUJWSjJYOUIxWkgxWTkzSklEU0MySDlZOFVSWEc6"
          // Authorization: "Basic FKR5BVJ2X9B1ZH1Y93JIDSC2H9Y8URXG"
        }
      };
      axios
        .get(url, header)
        .then(res => {
          resolve(res);
        })
        .catch(error => {
          reject(this.handleError(error));
        });
    });
  }

  _post(url, _data, contentType) {
    return new Promise(async (resolve, reject) => {
      let token_key = this.checkDomain(url);
      let header = await requestService.getAuthHeaders(contentType, token_key);
      axios
        .post(url, _data, header)
        .then(res => {
          resolve(res);
        })
        .catch(error => {
          reject(this.handleError(error));
        });
    });
  }

  _put(url, _data, contentType) {
    return new Promise(async (resolve, reject) => {
      let token_key = this.checkDomain(url);
      let header = await requestService.getAuthHeaders(contentType, token_key);
      axios
        .put(url, _data, header)
        .then(res => {
          resolve(res);
        })
        .catch(error => {
          reject(this.handleError(error));
        });
    });
  }

  _delete(url) {
    return new Promise(async (resolve, reject) => {
      let token_key = this.checkDomain(url);
      let header = await requestService.getAuthHeaders(token_key);
      axios
        .delete(url, header)
        .then(res => {
          resolve(res);
        })
        .catch(error => {
          reject(this.handleError(error));
        });
    });
  }

  _getWithToken(url, token) {
    return new Promise(async (resolve, reject) => {
      let header = {
        headers: {
          Authorization: `Bearer ${token}`
        }
      };
      // eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************.gA5o5z56ctDg4sx9rOEizg9ol8XuaZQgLmYsTz5qDWc
      axios
        .get(url, header)
        .then(res => {
          resolve(res);
        })
        .catch(error => {
          reject(this.handleError(error));
        });
    });
  }

  _postWithToken(url, _data, token) {
    return new Promise(async (resolve, reject) => {
      let header = {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json"
        }
      };
      axios
        .post(url, _data, header)
        .then(res => {
          resolve(res);
        })
        .catch(error => {
          reject(this.handleError(error));
        });
    });
  }

  _deleteWithToken(url, _data, token) {
    return new Promise(async (resolve, reject) => {
      let header = {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json"
        }
      };
      axios
        .delete(url, _data, header)
        .then(res => {
          resolve(res);
        })
        .catch(error => {
          reject(this.handleError(error));
        });
    });
  }

  handleError(error) {
    console.log(error);
    if (error.response) return error.response.data;
    else if (error.message) return error;
    else return "Server Internal Error!";
  }

  checkDomain(url) {
    var token = EnvConfig.BEAR_TOKEN;
    if (url.indexOf(EnvConfig.APP_API_URL) >= 0) {
      token = EnvConfig.API_TOKEN;
    }
    return token;
  }

  existDomain(url = "") {
    if (
      url.indexOf(EnvConfig.APP_API_URL) >= 0 ||
      url.indexOf(EnvConfig.APP_URL) >= 0
    ) {
      return true;
    }
    return false;
  }
}

export const httpService = new HttpService();
