import { ApiConfig } from "../../../configs/base.config";
import { httpService } from "../http.service";

function doLogin(obj) {
  try {
    const url = ApiConfig.App.Authorization.Login;
    return httpService._post(url, obj);
  } catch (e) {
  }
}

export function loginWithoutRedux(obj) {
  try {
    const url = ApiConfig.App.Authorization.Login;
    return httpService._post(url, obj);
  } catch (e) {
  }
}

export default {
  doLogin
};
