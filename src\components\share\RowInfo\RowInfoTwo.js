import React, { useState, useEffect } from "react";
import {
  Text,
  View,
  StyleSheet
} from "react-native";
import { Styles, Color } from "@common";
import Fontisto from 'react-native-vector-icons/Fontisto'

function RowInfoTwo(props) {

  return (
    <View style={[Styles.rowWrapper, Styles.Common.RowCenterBetween]}>
      <View style={Styles.Common.RowCenterLeft}>
        {
          props.icon && 
            <Fontisto name="ticket-alt" size={20} color="#000" />
        }
        <Text style={[Styles.txtLabel, {marginLeft: 4}]}>{props.label}</Text>
      </View>
      <Text style={Styles.txtValue}>
        <Text style={{color: Color.primary}}>{props.value}</Text> / {props.total}
      </Text>
    </View>
  );
}

export default RowInfoTwo;