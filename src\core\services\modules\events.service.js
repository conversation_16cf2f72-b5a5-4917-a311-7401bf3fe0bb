import { ApiConfig, EnvConfig } from "../../../configs/base.config";
import { httpService } from "../http.service";

function fetchDataEvents(obj, token) {
  console.log('SERVICES');
  try {
    const url = ApiConfig.App.Events.GetEvents.replace(
      "{{TYPE}}",
      obj.type
    );
      console.log(url);
    return httpService._getWithToken(url, token);
  } catch (e) {
    console.log(e);
  }
}

export default {
  fetchDataEvents
};