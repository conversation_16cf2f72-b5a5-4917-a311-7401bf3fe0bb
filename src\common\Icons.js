/**
 * Created by InspireUI on 21/12/2016.
 *
 * @format
 */

// Check out at 'http://ionicframework.com/docs/v2/ionicons/' for more icons
// 'react-native-vector-icons/Ionicons'
const Ionicons = {
  Menu: "ios-menu",
  Home: "ios-home",
  HomeOutline: "ios-home",
  Back: "ios-arrow-back",
  Forward: "ios-arrow-forward",
  Config: "ios-settings",
  More: "ios-more",
  SignIn: "ios-log-in",
  SignOut: "ios-log-out",
  ShoppingCart: "ios-cart",
  ShoppingCartEmpty: "ios-cart",
  Sort: "ios-funnel",
  Filter: "ios-funnel",
  ShowItem: "ios-arrow-dropright",
  HideItem: "ios-arrow-dropup",
  ListMode: "ios-list-box",
  GridMode: "ios-grid",
  Star: "ios-star",
  StarOutline: "ios-star",
  Wishlist: "ios-heart",
  WishlistEmpty: "ios-heart",
  Delete: "ios-trash",
  AddToCart: "ios-cart",
  MyOrder: "ios-cube",
  News: "ios-paper",
  Mail: "ios-mail",
  RatioOff: "ios-radio-button-off",
  RatioOn: "ios-radio-button-on",
  Search: "ios-search",
  Close: "ios-close",
  HappyFace: "ios-happy",
  SadFace: "ios-sad",
  ChatBubble: "ios-chatbubbles",
  ChatBubbleOutline: "ios-chatbubbles",
  Information: "ios-information",
  Person: "ios-person",
  PersonOutline: "ios-person",
  DropDown: "md-arrow-dropdown",
  Facebook: "logo-facebook",
  Google: "logo-google",
  Notifications: "ios-notifications",
  Camera: "logo-instagram",
  Contact: "ios-contact",
  Email: "md-mail",
  Lock: "md-lock",
  CheckCircle: "ios-checkmark-circle",
  RemoveCircle: "ios-remove-circle",
  PriceTags: "ios-pricetags",
  CheckMark: "ios-checkmark",
  Categories: "md-options",
  Down: "ios-arrow-down",
  Add: "ios-add",
  Remove: "ios-remove",
  DownArrow: "md-arrow-dropdown",
  RightArrow: "md-arrow-dropright"
};

// https://materialdesignicons.com/
// 'react-native-vector-icons/MaterialCommunityIcons'
const MaterialCommunityIcons = {
  Menu: "menu",
  Home: "home",
  SignIn: "login",
  Settings: "settings",
  SignOut: "logout",
  ShoppingCart: "cart", // old : cart
  ShoppingCartEmpty: "cart",
  ShoppingCartAdd: "cart-plus",
  ListMode: "view-agenda",
  GridMode: "view-grid",
  Star: "star",
  StarOutline: "star",
  Wishlist: "heart",
  WishlistEmpty: "heart",
  Delete: "delete",
  MyOrder: "truck-delivery",
  News: "newspaper",
  Mail: "mail",
  Search: "magnify",
  Facebook: "facebook",
  Google: "google",
  Email: "email",
  Lock: "lock",
  Category: "format-list-bulleted-type",
  CheckMark: "check",
  Back: "arrow-left",
  Forward: "arrow-right",
  Down: "arrow-down",
  Contact: "email",
  Categories: "format-list-bulleted",
  Pin: "map-marker",
  Setting: "settings",
  Apple: "apple"
};

export default { MaterialCommunityIcons, Ionicons };
