import React, { Component } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  FlatList,
  Platform,
  Text
} from "react-native";
import { WaitingLoader } from "@common";

import ProductView from "./productView";
import styles from "./listItemStyles";
import _ from "lodash";
import { fetchProductListing } from "../../core/services/modules/category.service";
import { fetchSearchProduct } from "../../core/services/modules/searchProduct.service";

const { width, height } = Dimensions.get("window");
export default class GridList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      products: this.props.products,
      ps: this.props.params,
      end: false //0 product
    };
    this.onEndReachedCalledDuringMomentum = true;
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (typeof nextProps.params !== "undefined") {
      this.setState({
        end: false,
        products: nextProps.products,
        ps: nextProps.params
      });
    }
  }

  _fetchProductListing = params => {
    fetchProductListing(params).then(res => {
      let queryRes = res.data.products ? res.data.products : res.data;
      if (res.status == 200 && queryRes.length > 0) {
        this.setState({
          isFetching: false,
          end: false,
          products: [...this.state.products, ...queryRes],
          ps: params
        });
      } else {
        this.setState({
          isFetching: false,
          end: true
        });

        return;
      }
    });
  };

  _fetchProductSearch = params => {
    fetchSearchProduct(params).then(res => {
      if (res.status == 200 && res.data && res.data.length > 0) {
        this.setState({
          isFetching: false,
          end: false,
          products: [...this.state.products, ...res.data],
          ps: params
        });
      } else {
        this.setState({
          isFetching: false,
          end: true
        });

        return;
      }
    });
  };

  _renderItems = ({ item, idx }) => {
    return (
      <View style={{ flexDirection: "row" }} key={idx}>
        {item.map(product => (
          <ProductView
            key={product.id}
            navigation={this.props.navigation}
            product={product}
            returnRoute={this.props.form}
            paramsCT={this.state.ps}
          />
        ))}
      </View>
    );
  };

  _handleLoadMore = () => {
    if (!this.state.isFetching && !this.state.end) {
      this.onEndReachedCalledDuringMomentum = true;

      if (this.props.form == "search") {
        this.setState(
          {
            isFetching: true,
            ps: {
              languageId: this.state.ps.languageId,
              keyword: this.state.ps.keyword,
              page: this.state.ps.page + 1,
              limit: this.state.ps.limit
            }
          },
          () => {
            this._fetchProductSearch(this.state.ps);
          }
        );
      }
      if (this.props.form == "listing") {
        this.setState(
          {
            isFetching: true,
            ps: {
              id: this.state.ps.id,
              type: this.state.ps.type,
              page: this.state.ps.page + 1,
              limit: this.state.ps.limit
            }
          },
          () => {
            this._fetchProductListing(this.state.ps);
          }
        );
      }
    }
  };

  _renderFooter = () => {
    return this.state.isFetching ? (
      <View style={{ marginTop: 30, alignItems: "center" }}>
        <WaitingLoader />
      </View>
    ) : null;
  };

  render() {
    if (!this.state.products) return <WaitingLoader />;
    let products = _.chunk(this.state.products, 2);
    return (
      <FlatList
        data={products}
        renderItem={this._renderItems}
        keyExtractor={(item, index) => index.toString()}
        onEndReached={this._handleLoadMore}
        onEndReachedThreshold={Platform.OS === "ios" ? 0 : 1}
        style={{ backgroundColor: "#fff", paddingHorizontal: 15 }}
        ListFooterComponent={this._renderFooter}
        showsVerticalScrollIndicator={false}
        onMomentumScrollBegin={() => {
          this.onEndReachedCalledDuringMomentum = false;
        }}
      />
    );
  }
}

const internalStyles = StyleSheet.create({
  item: {
    flexDirection: "column",
    width: (width - 40) / 2,
    marginBottom: 30,
    marginRight: 10
  },
  itemImg: {
    width: (width - 40) / 2,
    height: (width - 40) / 2 + 50,
    resizeMode: "cover",
    backgroundColor: "#f1f1f1"
  }
});
