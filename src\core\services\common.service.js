import { ApiConfig, EnvConfig } from "../../configs/base.config";
import numeral from "numeral";
import _ from "lodash";
import Storage from "react-native-storage";
import { httpService } from "./http.service";
import {AsyncStorage} from 'react-native'
import moment from "moment";

const storage = new Storage();

export const saveConfigSite = (config) => {
  return storage.save({
    key: "ConfigSite",
    data: config,
  });
};
export const loadConfigSite = () => {
  return storage
    .load({
      key: "ConfigSite",
      // autoSync: true,
      // syncInBackground: true
    })
    .then((res) => res)
    .catch(() => {
      storage.save({
        key: "ConfigSite",
        data: [],
      });

      return null;
    });
};

export const formatDate = (date) => {
  let newDate = "";
  newDate = moment(new Date(date)).format("DD/MM/YYYY");

  return newDate;
};

export const formatDateWithTime = (date) => {
  let newDate = Date.parse(date);
  let returnDate = "",
    returnTime = "";
  if (!isNaN(newDate)) {
    let tempDate = date.split(" ")[0].split("-");
    if (tempDate.length === 3) {
      returnDate = tempDate[2] + "/" + tempDate[1] + "/" + tempDate[0];
    }

    returnTime = date.split(" ").length > 1 ? date.split(" ")[1] : "";
  }

  return returnDate + " " + returnTime;
};

export const formatDateWithoutTime = (date) => {
  let newDate = Date.parse(date);
  let returnDate = "";
  if (!isNaN(newDate)) {
    let tempDate = date.split("-");
    if (tempDate.length === 3) {
      returnDate = tempDate[2] + "-" + tempDate[1] + "-" + tempDate[0];
    }
  }

  return returnDate;
};

export const manualFormatDateTime = (dt) => {
  if(dt) {
      let newDate = "";
      let dateArr = dt.split(" "); //seperate date & time
      let date = dateArr[0].split("-");
      let time = dateArr[1].split(":");

      newDate = `${date[2]}/${date[1]}/${date[0]}`;

      return newDate;
  }
  return "";
};

export function logOut(token) {
  try {
    const url = ApiConfig.App.Customer.LogOut;
    return httpService._getWithToken(url, token);
  } catch (e) {}
}

export const checkNamNhuan = (year) => {
  try {
    let year = parseInt(year);
    if (year % 100 == 0) {
      if (year % 400 == 0) {
        return true;
      } else {
        return false;
      }
    } else if (year % 4 == 0) {
      return true;
    } else {
      return false;
    }
  } catch (e) {}
};

export const validDay = (day, month) => {
  let month31Day = ["01", "03", "05", "07", "08", "10", "12"];
  let month30Day = ["04", "06", "09", "11"];
  let valid = true;
  if (month30Day.indexOf(month) >= 0 && parseInt(day) > 30) {
    valid = false;
  }
  return valid;
};
