import { createStore, applyMiddleware } from 'redux';
import createSagaMiddleware from 'redux-saga';
import { persistStore, persistReducer } from 'redux-persist'
import AsyncStorage from '@react-native-async-storage/async-storage';

// import sagas and reducer of home screen
import reducers from '../reducers';
import rootSaga from '../sagas/root';

import loggingMiddleware from './middleware/logging';

const sagaMiddleware = createSagaMiddleware();

const persistConfig = {
    key: 'root',
    storage: AsyncStorage,
    blacklist: ['order', 'contact']
}

const persistedReducer = persistReducer(persistConfig, reducers)

const store = createStore(
    persistedReducer,
    {},
    applyMiddleware(sagaMiddleware, loggingMiddleware)
);

sagaMiddleware.run(rootSaga);

const persistor = persistStore(store);

export {
    store,
    persistor
}
