import {
  LOGIN_REQUEST,
  LOGIN_SUCCESS,
  LOGIN_FAILURE,
  LOGIN_REQUEST_FB,
  LOGIN_REQUEST_GG,
  EMPTY_LOGIN,
  UPDATE_USER_INFO,
    AUTHORIZE_LOYALTIES_SUCCESS,
    AUTHORIZE_LOYALTIES_FAILURE
} from "../../actions/types.action";

const initialState = {
  data: {},
    loyalty: {},
  isFetching: false,
  isError: false
};

const loginReducer = (state = initialState, action) => {
  switch (action.type) {
    case LOGIN_REQUEST: {
      return {
        ...state,
        isFetching: true,
        isError: false,
          loginType: false,
      };
    }
    case LOGIN_SUCCESS: {
      return {
        ...state,
        isFetching: false,
        isError: false,
          loginType: false,
          loginMessage: '',
        data: {
          access_token: action.payload.data.token,
          token_refresh: action.payload.data.token_refesh,
          success: true,
          customer: {
            id: action.payload.data.id,
            firstname: action.payload.data.name,
            lastname: action.payload.data.lastname,
            email: action.payload.data.email,
            birthday: action.payload.data.birthday,
            role: action.payload.data.role,
            phone: action.payload.data.phone
          }
        }
      };
    }
    case LOGIN_FAILURE: {
      var message = "Người dùng lỗi";
      console.log('reducer');
      console.log(action);
      console.log('payload');
       if(action.payload.typeLogin){
           if (action.payload.message) {
               message = action.payload.message
           }
           return {
               ...state,
               loginMessage: message,
               typeLogin: {
                   typeLogin: action.payload.typeLogin,
                   customer: action.payload.customer,
               },
               isFetching: false,
               isError: true
           };
       }else{
           if(action.payload.errors){
               message = action.payload.errors
           }
           if(action.payload.error && action.payload.error.length > 0){
               message = action.payload.error[0]
           }

           return {
               ...state,
               loginMessage: message,
               isFetching: false,
               loginType: false,
               isError: true
           };
       }
    }
    case EMPTY_LOGIN: {
      return {
        ...state,
        isFetching: false,
        isError: false,
          loginType: false,
        data: {},
        loyalty: {}
      };
    }

    case UPDATE_USER_INFO: {
      return {
        ...state,
        isFetching: false,
        isError: false,
          loginType: false,
        data: action.payload
      };
    }

    case AUTHORIZE_LOYALTIES_SUCCESS: {
      return {
          ...state,
          loyalty: action.payload
      }
    }

    case AUTHORIZE_LOYALTIES_FAILURE: {
        return {
            ...state,
            loyalty: {}
        }
    }

    default: {
      return state;
    }
  }
};

export default loginReducer;
