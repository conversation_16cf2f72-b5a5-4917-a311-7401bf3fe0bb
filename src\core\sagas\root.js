import { all } from 'redux-saga/effects';
import { watchLogin } from './authorize/login.sagas';
import { watchHome } from './home/<USER>';
import { watchEvents } from './events/events.sagas';
import {watchEventScanner} from './eventscanner/eventscanner.sagas'
import {watchDoorlist} from './doorlist/doorlist.sagas'

export default function* rootSaga() {
    yield all([
        watchHome(),
        watchLogin(),
        watchEvents(),
        watchEventScanner(),
        watchDoorlist()
    ]);
}
