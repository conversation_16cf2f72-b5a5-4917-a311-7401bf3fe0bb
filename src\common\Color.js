/** @format */
export default {
    primary: '#FF3857', 
    secondary: '#9E9E9E',
    bodyColor: '#212121',
    success: '#44AA55',
    borderColor: '#EBEFF2',
    // navigation bar
    headerTintColor: "#1CB5B4",
    blue: '#306AF2',

    headingColor: '#212121',
    subColor: '#D9D9D9',

    // bottom tab bar
    tabbar: "rgba(255, 255, 255, 1)",
    tabbarTint: '#222',
    tabbarColor: "#ccc",

    // ////////////////////////////////////////////////////////////////////////////////
    // NOTE: THE BELOW COLOR MAY NOT RELATED TO YOUR REBRANDING & NOT COMMON STYLE
    // ////////////////////////////////////////////////////////////////////////////////

    // login screen color
    login: {
        facebook: "#3b5998",
        google: "#d34836",
    },

    // common
    error: "#f44336",
    accent: "#FFC107",
    accentLight: "#FFD54F",
    blackTextPrimary: "rgba(0,0,0,1)",
    blackTextSecondary: "rgba(0,0,0,0.5)",
    blackTextDisable: "rgba(0,0,0,0.3)",

    lightTextPrimary: "rgba(255,255,255,1)",
    lightTextSecondary: "rgba(255,255,255,255.5)",
    lightTextDisable: "rgba(255,255,255,0.3)",

    lightDivide: "rgba(255,255,255,0.12)",
    blackDivide: "rgba(0,0,0,0.05)",
    background: "white",
    DirtyBackground: "#F0F0F0",

    // Text
    Text: "#333",
    spin: "#333333",

    lightGrey: "rgba(247, 248, 250, 1)",
    lightGrey1: "rgba(212, 220, 255, 1)",
    darkOrange: "rgba(255, 132, 11, 1)",
    darkYellow: "rgba(255, 164, 31, 1)",
    yellow: "rgba(255, 198, 53, 1)",
    darkRed: "#8B0000",
    red: "#FF0000",
    lightgrey: "#999",
    green: "#2AB5B3",
    blue: "#0091ea",
    lightBlue: "#9ddaff",
    blue1: "rgba(30, 165, 233, 1)",
    blue2: "rgba(3, 207, 254, 1)",
    success:"#4CAF50",
    danger:"#f44336",
    warning:"#FF9800",

    starRating: "#FDF12C",
};
