/** @format */

import { Dimensions, StyleSheet, I18nManager, Platform } from "react-native";
import { Color, Styles, Constants } from "@common";

const { width, height } = Dimensions.get("window");

export default StyleSheet.create({
  container: {
    flex: 1
  },
  logoWrap: {
    ...Styles.Common.ColumnCenter,
    height: height/3,
    width: width,
  },
  iconButton: {
    padding: 10,
    zIndex:10
  },
  logo: {
    width: Styles.width * 0.6,
    height: (Styles.width * 0.6) / 2
  },
  subContain: {
    paddingHorizontal: Styles.width * 0.1,
    paddingBottom: 50,
    flexGrow: 2,
    width: width,
  },
  btnSend: {
    marginTop: 15,
    marginBottom: 15,
    width: 200
  },
  separatorWrap: {
    paddingVertical: 15,
    flexDirection: "row",
    alignItems: "center"
  },
  separator: text => ({
    borderBottomWidth: 1,
    flexGrow: 1,
    borderColor: text
  }),
  separatorText: text => ({
    color: text,
    paddingHorizontal: 10
  }),
  fbButton: {
    backgroundColor: Color.login.facebook,
    borderRadius: 25,
    elevation: 1
  },
  ggButton: {
    marginVertical: 10,
    backgroundColor: Color.login.google,
    borderRadius: 25
  },
  signUp: {
    color: Color.blackTextSecondary,
    fontFamily: Constants.fontFamily,
    paddingVertical: 10
  },
  highlight: {
    color: Color.primary,
    fontFamily: Constants.fontFamily
  },
  overlayLoading: {
    ...StyleSheet.absoluteFillObject,
    width,
    height
  },
  modalForgot: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20
  },
  modalForgotInner: {
    padding: 15,
    backgroundColor: "#fff",
    maxWidth:500
  },
  modalForgotHeader:{
      paddingBottom:10,
      borderBottomWidth:1,
      borderBottomColor:'#ddd',
      marginBottom: 10,
  },
  txtSub: {
    fontSize: 20,
    fontFamily: Constants.fontFamilyBold,
    marginTop: 30,
    color: '#000'
  }
});
