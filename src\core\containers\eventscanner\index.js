import { connect } from "react-redux";
import EventScanner from "../../../components/screens/eventscanner";
import {
  checkNft
} from "../../actions/eventscanner/eventscanner.actions";
import {
  emptyTicket,
} from "../../actions/ticketdetail/ticketdetail.actions";

const mapStateToProps = state => {
  return {
    eventscanner: state.eventscanner
  };
};

const mapDispatchToProps = dispatch => {
  return {
    checkNft: obj => dispatch(checkNft(obj)),
    emptyTicket: () => dispatch(emptyTicket()),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(EventScanner);
