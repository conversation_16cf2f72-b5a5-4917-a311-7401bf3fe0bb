import React, {Component, useState, useEffect} from "react";
import {
    View,
    Image,
    TextInput,
    Text,
    TouchableWithoutFeedback,
    ScrollView
} from "react-native";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import {Styles, Images} from "@common";
import {Spin<PERSON>, ButtonIndex, Button} from "@share";
import styles from "./styles";

import validateInput from "../../../../src/common/ValidateWraper";
import ErrorMessage from "../../common/errorMessage";
import Feather from 'react-native-vector-icons/Feather'

function LoginScreen(props){
  // const [username, setUsername] = useState("abc123");
  // const [password, setPassword] = useState("12345@789");
  // const [username, setUsername] = useState("iiooll2");
  // const [password, setPassword] = useState("123456");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [usernameError, setUsernameError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [loginMessage, setLoginMessage] = useState("");
  const [isLoading, setLoading] = useState(false);
  const [toggleIcon, setToggleIcon] = useState('eye')
  const [showPassword, setShowPassword] = useState(true);
  const [langOpt, setLangOpt] = useState(1);

  useEffect(() => {

    setLoading(props.loginResponse.isFetching);
    if(!props.loginResponse.isFetching && props.loginResponse.data.success){
      if(props.loginResponse.data.access_token && props.loginResponse.data.customer){
        console.log('LOGIN RESPONSE')
        console.log(props.loginResponse);
        AsyncStorage.multiSet([
          ["ACCESS_TOKEN", props.loginResponse.data.access_token.toString()],
          [
              "USER_ID",
              props.loginResponse.data.customer.id
                  ? props.loginResponse.data.customer.id.toString()
                  : "0"
          ]
        ]);

        setLoginMessage("");
        props.navigation.navigate(`AppDrawerNavigator`);
      }
    }

    if (
      (!props.loginResponse.isFetching &&
      props.loginResponse.data.success === false) ||
      (!props.loginResponse.isFetching &&
      props.loginResponse.isError === true)
    ) {
      if(props.loginResponse.loginMessage){
        setLoginMessage(props.loginResponse.loginMessage);
      }
    }

  });

  const onLoginSubmit = () => {
    setLoginMessage("");
    const regUsername = /^[a-z0-9_-]{6,24}$/;
    let txtUsernameError = "";
    let txtUsername = username.trim();

    if (txtUsername == "") {
      txtUsernameError = "Vui lòng nhập Username";
    } else if (
      regUsername.test(txtUsername) == false
    ) {
      txtUsernameError = "Username không hợp lệ";
    } else {
      txtUsernameError = "";
    }

    let txtPasswordError = validateInput("password", password);
    setUsernameError(txtUsernameError);
    setPasswordError(txtPasswordError);

    if (txtUsernameError === "" && txtPasswordError === "") {
      setLoading(true);
      props._doLogin({
        username: username.trim(),
        password: password.trim(),
      });
    }
    // props.navigation.navigate(`AppDrawerNavigator`);
  }

  const toggleShowPassword = () => {
    if (toggleIcon == 'eye'){
      setToggleIcon('eye-off')
      setShowPassword(false)
    }else{
      setToggleIcon('eye')
      setShowPassword(true)
    }
  }

  const onToggleGroup = () => {
    if (langOpt == 1){
      setLangOpt(2)
    }else{
      setLangOpt(1)
    }
  }

  return (
    <View style={{flex: 1, position: "relative"}} >
      <KeyboardAwareScrollView
        enableOnAndroid
        style={{backgroundColor: "#fff"}}
      >
        <View style={Styles.Common.ColumnCenterBetween}>
          <View style={styles.container}>
            <View style={styles.logoWrap}>
              <Image
                source={Images.mainLogo}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.txtSub}>Dành cho nhà tổ chức</Text>
            </View>
            <View style={styles.subContain}>
              <View style={Styles.Common.ColumnCenter}>
                {isLoading && <Spinner mode="normal" size="small"/>}

                {(loginMessage ||
                  usernameError ||
                  passwordError ) && (
                    <View style={Styles.Common.containerErrorMessage}>
                      <ErrorMessage message={loginMessage}/>
                      <ErrorMessage message={usernameError}/>
                      <ErrorMessage message={passwordError}/>
                    </View>
                )}
                <View
                  style={[
                    Styles.Common.inputWrap,
                    usernameError !== "" ? Styles.Common.inputError : null
                  ]}
                >
                  <TextInput
                    style={[Styles.Common.input]}
                    underlineColorAndroid="transparent"
                    placeholderTextColor="#999"
                    placeholder="Username"
                    // keyboardType="email-address"
                    value={username}
                    name="username"
                    onChangeText={text => setUsername(text.trim())}
                  />
                  {username !== "" && (
                    <TouchableWithoutFeedback
                        onPress={() => setUsername("")}
                    >
                      <Feather
                        name="x"
                        size={18}
                        style={{marginRight: 10}}
                        color="#999"
                      />
                    </TouchableWithoutFeedback>
                  )}
                </View>
                <View
                  style={[
                    Styles.Common.inputWrap,
                    passwordError !== ""
                      ? Styles.Common.inputError
                      : null
                  ]}
                >
                  <TextInput
                      style={[Styles.Common.input]}
                      underlineColorAndroid="transparent"
                      placeholderTextColor="#999"
                      placeholder="Password"
                      secureTextEntry={showPassword}
                      value={password}
                      name="password"
                      onChangeText={text => setPassword(text.trim())}
                  />
                  {
                    (toggleIcon == 'eye') ? 
                    <TouchableWithoutFeedback
                      onPress={() => toggleShowPassword()}
                      style={styles.iconButton}
                    >
                      <Feather
                        name='eye'
                        size={18}
                        style={{marginRight: 10}}
                        color="#999"
                      />
                    </TouchableWithoutFeedback>
                    :
                    <TouchableWithoutFeedback
                      onPress={() => toggleShowPassword()}
                      style={styles.iconButton}
                    >
                      <Feather
                        name='eye-off'
                        size={18}
                        style={{marginRight: 10}}
                        color="#999"
                      />
                    </TouchableWithoutFeedback>
                  }
                </View>
                <ButtonIndex
                    text="Sign In"
                    disabled={isLoading}
                    containerStyle={styles.btnSend}
                    onPress={() => onLoginSubmit()}
                />
              </View>
            </View>
          </View>
          <View style={{paddingBottom: 30, paddingTop: 80}}>
            <Button 
              leftTxt = "ENG"
              rightTxt = "VN"
              onToggleGroup={() => onToggleGroup()}
              type="group"
              activeBtn={langOpt}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
}

LoginScreen.navigationOptions = ({ navigation }) => {
  return {
    header: null
  }
}

export default LoginScreen;
