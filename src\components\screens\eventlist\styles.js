/** @format */

import { Dimensions, StyleSheet, I18nManager, Platform } from "react-native";
import { Color, Styles, Constants } from "@common";

const { width, height } = Dimensions.get("window");

export default StyleSheet.create({
    tabButtonWrapper: {
        paddingVertical: 10,
    },
    tabButton: {
        paddingHorizontal: 10,
        paddingVertical: 14,
        borderRadius: 8,
        backgroundColor: Color.secondary,
        flex: 1,
        marginHorizontal: 3
    },
    txtTabButton: {
        fontSize: 12,
        fontFamily: Constants.fontFamilyBold,
        color: 'white',
        textAlign: 'center'
    },
    tabButtonActive: {
        backgroundColor: Color.primary
    },
    listView: {
        width: '100%',
        minHeight: 400
    },
    listWrapper: {
        flex: 1,
        paddingTop: 10,
    }
});
