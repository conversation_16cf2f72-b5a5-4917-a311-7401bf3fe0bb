import React, {Component} from "react";
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    ScrollView,
    Text
} from "react-native";
import {Styles, Images, Constants, Device, Color} from "@common";
import Feather from "react-native-vector-icons/Feather"
import {connect} from "react-redux";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { emptyLogin } from "../../../core/actions/authorize/login.action";
import {
  logOut,
} from "../../../core/services/common.service";

function DrawerContent(props) {

    const onLogout = () => {
        AsyncStorage.getItem("ACCESS_TOKEN").then((val) => {
            logOut(val).then((logOutResponse) => {
                console.log(logOutResponse);
            });

            props._emptyLogin();
            AsyncStorage.multiRemove(["ACCESS_TOKEN", "USER_ID"]).then((res) => {
                props.navigation.reset({
                    index: 0,
                    routes: [{ name: 'Login' }],
                });
            });
        });
    
    };
    let {navigation} = props;
    return (
        <View style={styles.container}>
            <View style={styles.logoOuter}>
                <View style={Styles.ColumnCenter}>
                    <Image
                        source={Images.mainLogo}
                        style={styles.image}
                        resizeMode="contain"
                    />
                    {
                        props.loggedUser.customer &&
                            <Text style={{textAlign: 'center', marginTop: 14, fontFamily: Constants.fontFamilyBold}}>{props.loggedUser.customer.firstname} {props.loggedUser.customer.lastname}</Text>
                    }
                    
                </View>
            </View>
            <ScrollView>
                <View style={styles.sideMenu}>
                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={() => {
                            navigation.navigate("EventListScreen");
                            navigation.closeDrawer();
                        }}
                    >
                        <View style={styles.menuItem} >
                            <View>
                                <Feather size={18} color={Color.headingColor} name="database"/>
                            </View>
                            <Text style={styles.menuItemCaption}>Event List</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={() => onLogout()}
                    >
                        <View style={styles.menuItem}>
                            <View>
                                <Feather size={18} color={Color.headingColor} name="log-out"/>
                            </View>
                            <Text style={styles.menuItemCaption}>Đăng xuất</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </View>
    );
}

const mapStateToProps = state => {
    return {
      loggedUser: state.login.data,
    };
};
  
const mapDispatchToProps = dispatch => {
    return {
        _emptyLogin: () => {
            dispatch(emptyLogin());
        },
    };
};
export default connect(
    mapStateToProps,
    mapDispatchToProps
)(DrawerContent);


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#fff"
    },
    logoOuter: {
        padding: 30,
        borderBottomWidth: 1,
        borderColor: "#eee",
        alignItems: "center",
        paddingTop: 45
    },
    image: {
        width: 200,
        height: 70,
        resizeMode: "contain"
    },
    sideMenu: {flex: 1},
    menuItem: {
        height: 50,
        alignItems: "center",
        paddingLeft: 20,
        flexDirection: "row",
        borderBottomWidth: 1,
        borderBottomColor: "#eee"
    },
    menuItemCaption: {
        color: "#222",
        marginLeft: 20,
        fontFamily: Constants.fontFamily
    },
    icon: {width: 18, height: 18, resizeMode: "contain"},
    socialImageOuter: {
        width: 32,
        height: 32,
        justifyContent: "center",
        alignItems: "center",
        marginRight: 20
    },
    socialImage: {
        width: 16,
        height: 16
    },
    iconArrow: {color: "#999"},
    footer: {
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f1f1f1",
        padding: 10,
        paddingBottom: Device.isIphoneX ? 30 : 10
    },
    footerCaption: {fontFamily: Constants.fontFamily, color: "#999"}
});
