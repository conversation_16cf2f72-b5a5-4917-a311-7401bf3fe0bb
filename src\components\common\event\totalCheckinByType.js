import React, { PureComponent, useEffect } from "react";
import { View, Text, StyleSheet } from "react-native";
import { Images, Constants, Color, Styles } from "@common";

import HeadingTwo from "@share/Heading/HeadingTwo"
import RowInfoTwo from "@share/RowInfo/RowInfoTwo";

export default function TotalCheckinByType(props){

  useEffect(() => {

  }, [props.data])

  return(
    <View >
      <HeadingTwo title="TOTAL CHECKED IN BY TICKET TYPE" />
      {
        props.data && props.data.map((zones, index) => {
          return zones.seats.map((seat, i) => { 
            return (
              <RowInfoTwo key={index + i} icon="ticket" label={"Zone: " + zones.zone_name + " - Row: " + seat.row_code} value={seat.totalCheckin} total={seat.totalTickets}/>
            );
          })
        })
      }
    </View>
  );
}
