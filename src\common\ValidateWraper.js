import validationConstraints from "./ValidationConstraints";
import validate from "validate.js";

export default function validateInput(fieldName, value) {

  var formValues = {};
  if (value !== "") {
    formValues[fieldName] = value;
  }

  var formFields = {}
  formFields[fieldName] = validationConstraints[fieldName];

  const result = validate(formValues, formFields);

  if (result) {
    return result[fieldName][0];
  }

  return "";
}
