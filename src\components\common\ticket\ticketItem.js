import React, { PureComponent } from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { Images, Constants, Color, Styles } from "@common";
import Ionicons from 'react-native-vector-icons/Ionicons'

export default function TicketItem ({item, onPress}){
  return(
    <TouchableOpacity style={styles.touchView} onPress={() => onPress()}>
      <View style={[Styles.Common.RowCenterBetween, styles.wrapperView]}>
        <View style={Styles.Common.RowCenterLeft}>
          <Ionicons name="checkmark-circle" size={22} color={item.checked_time ? Color.primary : Color.subColor} /> 
          <View style={styles.ticketInfo}>
              <Text style={styles.txtName}>{item.cart_temp.email}</Text>
              <Text style={styles.txtSub}>Zone: {item.row_code} - Seat: {item.seat_name}</Text>
          </View>
        </View>
        <View style={styles.wrapperRight}>
          <Text style={styles.txtPrice}>+84 {item.cart_temp.phone}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}


const styles = StyleSheet.create({
  wrapperView: {
    borderBottomWidth: 1,
    borderColor: Color.subColor,
    paddingVertical: 6,
    paddingHorizontal: Constants.paddingScreen,
    paddingRight: 35
  },
  txtName: {
    fontFamily: Constants.fontFamilyBold,
    color: '#000',
    fontSize: 10,
  },
  txtSub: {
    fontFamily: Constants.fontFamily,
    color: '#000',
    fontSize: 10,
  },
  txtPrice: {
    fontSize: 12,
    fontFamily: Constants.fontFamily,
    color: '#000'
  },
  ticketInfo: {
    paddingLeft: 6
  }

});