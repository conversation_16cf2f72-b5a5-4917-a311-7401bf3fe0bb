import React, {PureComponent} from "react";
import {SafeAreaView, View} from "react-native";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";

export default function WaitingBlog() {
    return (
        <SafeAreaView style={{backgroundColor: 'white', flex: 1, padding: 12,}}>
            <View style={{marginBottom: 12}}>
                <SkeletonPlaceholder>
                    <View style={{flexDirection: 'column'}}>
                        <View style={{width: '100%', height: 230}}></View>

                        <View
                            style={{
                                justifyContent: "space-between",
                                marginTop: 12,
                                flex: 1
                            }}
                        >
                            <View style={{width: "50%", height: 20}}/>
                            <View style={{width: "30%", height: 20, marginTop: 12,}}/>
                            <View style={{width: "80%", height: 20, marginTop: 12,}}/>
                        </View>
                    </View>
                </SkeletonPlaceholder>
            </View>
        </SafeAreaView>
    );
}
