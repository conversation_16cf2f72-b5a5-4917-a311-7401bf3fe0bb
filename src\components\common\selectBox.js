import React, { PureComponent } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import {Styles, Constants} from '@common';

export default class SelectBox extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  render() {
      var {placeholder, value, onPress, style} = this.props;
    return (
      <View style={[Styles.Common.inputWrap, style]}>
        <TouchableOpacity onPress={()=> onPress()} style={Styles.Common.input}>
                <Text style={{color: value !== '' ? "#222" : "#999", marginTop:3, fontFamily:Constants.fontFamily}}>
                    {value !== '' ? value : placeholder}
                </Text>
        </TouchableOpacity>
        <MaterialCommunityIcons name="chevron-down" style={Styles.Common.inputWrapIconRight}/>
      </View>
    );
  }
}
