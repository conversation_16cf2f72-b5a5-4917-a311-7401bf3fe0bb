import React, {useEffect} from 'react';
import type {Node} from 'react';
import {
    Platform,
    StatusBar,
    StyleSheet,
    View,
} from 'react-native';
import {Provider} from "react-redux";
import {PersistGate} from "redux-persist/integration/react";
import {store, persistor} from "./src/core/store";
import {SafeAreaProvider} from "react-native-safe-area-context";
import Navigator from "./src/core/routes/navigatorConfig";


const App: () => Node = () => {
    return (
        <Provider store={store}>
            <PersistGate loading={null} persistor={persistor}>
                <View style={styles.container}>
                    {Platform.OS === "ios" ? (
                        <StatusBar barStyle="default"/>
                    ) : (
                        <StatusBar barStyle="light-content" backgroundColor="#999"/>
                    )}
                    <SafeAreaProvider>
                        <Navigator/>
                    </SafeAreaProvider>
                </View>
            </PersistGate>
        </Provider>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#fff",
        position: "relative",
    },
});


export default App;
