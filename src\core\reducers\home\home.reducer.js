import {
    DASHBOARD_REQUEST,
    DASH<PERSON>ARD_SUCCESS,
    DASHBOARD_FAILURE,
} from '../../actions/types.action';

const initialState = {
    data: {},
    isFetching: true,
    isError: false,
};
const dasboardReducer = (state = initialState, action) => {
    switch (action.type) {
        case DASHBOARD_REQUEST: {
            return {
                ...state,
                isFetching: true,
                isError: false,
                data: {},
            };
        }
        case DASHBOARD_SUCCESS: {
            return {
                ...state,
                isFetching: false,
                isError: false,
                data: action.payload
            };
        }
        case DASHBOARD_FAILURE: {
            return {
                ...state,
                isFetching: false,
                isError: false
            };
        }
        default: {
            return state;
        }
    }
};

export default dasboardReducer;