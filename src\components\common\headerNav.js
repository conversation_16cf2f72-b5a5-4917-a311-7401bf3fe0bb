import React, { PureComponent } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Animated,
  Easing
} from "react-native";
import { Constants, Device, Images } from "@common";

export default class HeaderNav extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      opacity: new Animated.Value(0)
    };
  }
  componentDidMount() {
    Animated.timing(this.state.opacity, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true
    }).start();
  }

  // componentWillUnmount(){
  //   this.props.navigation.setParams({
  //     backRoute: 'appDrawer'
  //   })
  // }

  render() {
    var { title, type, navigation, style, backRoute } = this.props;
    return (
      <View style={[styles.headerContainer, style]}>
        {type === "menu" ? (
          <TouchableOpacity
            style={{ width: 20, height: 20 }}
            onPress={() => navigation.openDrawer()}
            activeOpacity={0.8}
          >
            <Animated.Image
              source={Images.navigationIcons.menu}
              style={{
                width: '100%',
                height: '100%',
                resizeMode: "contain",
                opacity: this.state.opacity
              }}
            ></Animated.Image>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={{ width: 40, height: 30 }}
            onPress={() =>
              navigation.goBack()
            }
            activeOpacity={0.8}
          >
            <Animated.Image
              source={Images.navigationIcons.arrowLeft}
              style={{
                width: 20,
                height: 20,
                resizeMode: "contain",
                opacity: this.state.opacity,
                marginLeft: 15,
                marginTop: 5
              }}
            ></Animated.Image>
          </TouchableOpacity>
        )}
        <Animated.Text
          style={[styles.headerTitle, { opacity: this.state.opacity }]}
        >
          {title}
        </Animated.Text>
        <Image
          source={Images.navigationIcons.arrowLeft}
          style={{ width: 20, height: 20, resizeMode: "contain", opacity: 0 }}
        ></Image>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingRight: 15,
    paddingBottom: 20,
    paddingTop: Platform.OS === "ios" ? (Device.isIphoneX ? 45 : 30) : 20,
    backgroundColor: "#fff"
  },
  headerTitle: {
    flex: 1,
    fontWeight: "normal",
    fontFamily: Constants.fontFamily,
    fontSize: 20,
    color: "#222",
    textAlign: "center"
  }
});
