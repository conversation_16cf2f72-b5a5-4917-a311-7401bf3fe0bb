# Override Firebase SDK Version
$FirebaseSDKVersion = '10.24.0'
$RNFirebaseAsStaticFramework = true

require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, '13.4'
prepare_react_native_project!

use_frameworks! :linkage => :static
pod 'Firebase', :modular_headers => true
pod 'FirebaseCoreInternal', :modular_headers => true
pod 'GoogleUtilities', :modular_headers => true
use_modular_headers!
