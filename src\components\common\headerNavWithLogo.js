import React, { PureComponent } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Image,
} from "react-native";
import { Images, Device, Styles } from "@common";
import Feather from 'react-native-vector-icons/Feather';
import LinearGradient from 'react-native-linear-gradient';

export default class HeaderNavWithLogo extends PureComponent {
  constructor(props) {
    super(props);
  }

  render() {
    var { navigation, style } = this.props;
    return (

      <LinearGradient colors={['#FF3857', '#306AF2']} useAngle={true} angle={45} style={styles.headerContainer}>
        <TouchableOpacity
          onPress={() => navigation.openDrawer()}
          activeOpacity={0.8}
          style={{ width: 24, height: 24 }}
        >
          <Feather 
            name="align-left"
            size={28}
            color="#fff"
            />

        </TouchableOpacity>
        <View style={styles.headerTitle}>
          <TouchableOpacity
            onPress={() => navigation.navigate("EventListScreen")}
            activeOpacity={0.8}
          >
            <Image
              source={Images.mainIcon}
              style={[Styles.Common.headerLogo]}
              resizeMode="stretch"
            />
          </TouchableOpacity>
        </View>
        <View style={{width: 24}}></View>
      </LinearGradient>
    );
  }
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 15,
    paddingBottom: 10,
    paddingTop: Platform.OS === "ios" ? (Device.isIphoneX ? 45 : 30) : 10,
    backgroundColor: "#fff",
  },
  headerTitle: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
  },
});
