import {
    EVENT_SCANNER_REQUEST, 
    EVENT_SCANNER_SUCCESS, 
    EVENT_SCANNER_FAILURE, 
    EMPTY_TICKET,
    SELECT_EVENT_DETAIL
} from '../../actions/types.action';

const initialState = {
    data: {},
    isFetching: true,
    isError: false,
    selectedEvent: {}
};
const eventScannerReducer = (state = initialState, action) => {
    switch (action.type) {
        case EVENT_SCANNER_REQUEST: {
            return {
                ...state,
                isFetching: true,
                isError: false,
                data: {},
            };
        }
        case EVENT_SCANNER_SUCCESS: {
            return {
                ...state,
                isFetching: false,
                isError: false,
                data: action.payload
            };
        }
        case EVENT_SCANNER_FAILURE: {
            return {
                ...state,
                isFetching: false,
                isError: false
            };
        }
        
        case EMPTY_TICKET: {
            console.log('EMPTY_TICKET');
          return {
            ...state,
            isFetching: false,
            isError: false,
            data: {},
          };
        }
        case SELECT_EVENT_DETAIL: {
            return {
                ...state,
                selectedEvent: action.payload
            }
        }
        default: {
            return state;
        }
    }
};

export default eventScannerReducer;