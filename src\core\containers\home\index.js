import { connect } from "react-redux";
import HomeScreen from "../../../components/screens/home";

import {
  fetchDashboard
} from "../../actions/home/<USER>";

const mapStateToProps = state => {
  return {
    eventscanner: state.eventscanner,
    dashboard: state.dashboard
  };
};

const mapDispatchToProps = dispatch => {
  return {
    fetchDashboard: obj => dispatch(fetchDashboard(obj)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(HomeScreen);
