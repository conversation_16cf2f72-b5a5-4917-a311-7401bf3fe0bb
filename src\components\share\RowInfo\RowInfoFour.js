import React, { useState, useEffect } from "react";
import {
  Text,
  View,
  StyleSheet,
  Platform
} from "react-native";
import { Styles, Constants, Color } from "@common";

function RowInfoFour(props) {
    return (
        <View style={[Styles.rowWrapper, Styles.Common.RowCenterBetween]}>
            <Text style={styles.txtLabel}>{props.label}</Text>
            <Text style={styles.txtValue}>{props.value}</Text>
        </View>
    );
}

export default RowInfoFour;

const styles = StyleSheet.create({
  txtLabel:{
    fontSize: Platform.OS == 'ios' ? 14 : 10,
    fontFamily: Constants.fontFamily,
    color: '#000'
  },
  txtValue: {
    fontSize: Platform.OS == 'ios' ? 14 : 12,
    fontFamily: Constants.fontFamily,
    color: Color.primary,
    textAlign: 'center'
  }
});