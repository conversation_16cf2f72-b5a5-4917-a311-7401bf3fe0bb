import {
    DOOLIST_LIST_REQUEST, 
    DOOLIST_LIST_SUCCESS, 
    DOOLIST_LIST_FAILURE
} from '../../actions/types.action';

const initialState = {
    data: {},
    isFetching: true,
    isError: false,
};
const doorlistReducer = (state = initialState, action) => {
    switch (action.type) {
        case DOOLIST_LIST_REQUEST: {
            return {
                ...state,
                isFetching: true,
                isError: false
            };
        }
        case DOOLIST_LIST_SUCCESS: {
            return {
                ...state,
                isFetching: false,
                isError: false,
                data: action.payload
            };
        }
        case DOOLIST_LIST_FAILURE: {
            return {
                ...state,
                isFetching: false,
                isError: false
            };
        }
        default: {
            return state;
        }
    }
};

export default doorlistReducer;