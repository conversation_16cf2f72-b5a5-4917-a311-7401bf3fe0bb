import * as Types from '../../actions/types.action';

const initialState = {
    configSite: []
};

const configSiteReducer = (state = initialState, action) => {
    switch (action.type) {
        case Types.HOME_CONFIG_SITE_SUCCESS: {
            return {
                ...state,
                configSite: action.payload,
            };
        }
        default: {
            return state;
        }
    }
};

export default configSiteReducer;