import React, { PureComponent } from "react";
import { View, Text, StyleSheet } from "react-native";
import { Images, Constants, Color, Styles } from "@common";

export default function TotalCheckin (props){
  const current_percent = props.total ? ((props.current ? props.current : 0) * 100 / props.total) : 100;
  return(
    <View style={[Styles.sectionWrapper, Styles.Common.ColumnCenter]}>
      <Text style={styles.txtText}>TOTAL CHECKIN</Text>
      <Text style={styles.txtTotal}>
        <Text style={{color: Color.primary}}>{props.current}</Text> / {props.total}
      </Text>
      <View style={styles.loadingTotal}>
        <View style={[styles.currentTotal, {width: current_percent + '%'}]}></View>
      </View>
    </View>
  );
}


const styles = StyleSheet.create({
  txtText: {
    fontFamily: Constants.fontFamilyBold,
    color: Color.subColor,
    fontSize: 15,
    marginBottom: 10,
  },
  txtTotal: {
    fontSize: 30,
    fontFamily: Constants.fontFamilyBold,
    color: '#000000',
    marginBottom: 10,
  },
  loadingTotal: {
    width: '100%',
    height: 24,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: Color.subColor,
  },
  currentTotal: {
    backgroundColor: Color.primary,
    height: 22,
    borderRadius: 100,
  }

});