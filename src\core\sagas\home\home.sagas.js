import {
    DASH<PERSON>ARD_REQUEST,
    DASH<PERSON>ARD_SUCCESS,
    DASHBOARD_FAILURE,
  } from "../../actions/types.action";
  
  import AsyncStorage from '@react-native-async-storage/async-storage';
  import services from "../../services/modules/home.service";
  import { put, call, takeLatest } from "redux-saga/effects";
  
  function* fetDataTask(obj) {
    try {
      const token = yield AsyncStorage.getItem("ACCESS_TOKEN");
      console.log('fetach data Task')
      console.log(obj.payload)
      const res = yield call(services.fetchDashboard, obj.payload, token);
      if ((res.status === 200 || res.status === 201) && res.data !== null) {
        // console.log("=======================")
        // console.log(res.data.data)
        // console.log("=======================")
        yield put({
          type: DASHBOARD_SUCCESS,
          payload: res.data.data
        });
      } else {
        yield put({
          type: DASHBOARD_FAILURE,
          payload: res.data
        });
      }
    } catch (error) {
      yield put({
        type: DASHBOARD_FAILURE,
        payload: error
      });
    }
  }
  
  export function* watchHome() {
    yield takeLatest(DASHBOARD_REQUEST, fetDataTask);
  }
  