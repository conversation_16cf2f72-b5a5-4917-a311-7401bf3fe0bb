import { Dimensions, StyleSheet, I18nManager, Platform } from "react-native";
import { Color, Styles, Constants } from "@common";

const { width, height } = Dimensions.get("window");

export default StyleSheet.create({
    validWrap: {
        padding: Constants.paddingScreen,
    },
    txtButton: {
        fontSize: 15,
        fontFamily: Constants.fontFamilyBold,
        color: 'white'
    },
    txtValid: {
        fontSize: 20,
        color: Color.primary,
        fontFamily: Constants.fontFamilyBold,
        marginBottom: 5,
    }
});
