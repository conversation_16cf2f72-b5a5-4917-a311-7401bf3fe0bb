import configSiteReducer from "./home/<USER>";
import loginReducer from "./authorize/login.reducer";
import eventsReducer from "./events/events.reducer";
import eventScannerReducer from './eventscanner/eventscanner.reducer';
import doorlistReducer from './doorlist/doorlist.reducer';
import dasboardReducer from './home/<USER>';
import { combineReducers } from "redux";

const rootReducer = combineReducers({
    home: configSiteReducer,
    login: loginReducer,
    events: eventsReducer,
    eventscanner: eventScannerReducer,
    doorlist: doorlistReducer,
    dashboard: dasboardReducer
});

export default rootReducer;
