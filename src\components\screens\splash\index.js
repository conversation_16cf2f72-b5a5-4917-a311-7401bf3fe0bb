import React, { useState, useEffect, Component } from "react";
import { View, Text, Image, AsyncStorage, Platform, Alert, Linking } from "react-native";
import { Images } from "@common";
import { Spinner } from "@share";
import { connect } from "react-redux";
import { EnvConfig } from "../../../configs/base.config";
// import {
//   fetchConfigSite,
//   fetchEvents
// } from "../../../core/actions/home/<USER>";
// import checkVersion from 'react-native-store-version';
// import {request, check, PERMISSIONS, RESULTS} from 'react-native-permissions';

function SplashScreen(props) {
  const isLoading = useState(true);
  const versionApp = useState((Platform.OS === 'ios') ? EnvConfig.IOS_VERSION : EnvConfig.ANDROID_VERSION);

  useEffect(() => {
    if (props.loggedUser !== null) {  
      if(props.loggedUser.access_token !== undefined){
        props.navigation.navigate("AppDrawerNavigator");  
      }else{
        props.navigation.navigate("Login");
      }
    }
  }, [])
  
  return (
    <View style={{ justifyContent: "center", alignItems: "center", flex: 1 }}>
      <Image
        source={Images.mainLogo}
        style={{ width: 150, height: 200, resizeMode: "contain" }}
      />
      {isLoading ? <Spinner mode="normal" size="small" /> : null}
    </View>
  );
}

const mapStateToProps = state => {
  return {
    loggedUser: state.login.data,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    // _fetchConfigSite: () => {
    //   dispatch(fetchConfigSite());
    // },
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(SplashScreen);
